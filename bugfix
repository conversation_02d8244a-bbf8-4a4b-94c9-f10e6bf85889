----------------BUG FIX------------------
(/) Refresh view when you send data (currently nothing gets updated)
(/)Remove background colors when playing in record mode
(/)Make the right drawer scrollable and remove scroll on main page
(/)Fix issue with removing elements that are part of a sequence
(/)Fix text sur les elements
(/)Make dummy to be used for linking elements and senddata
(/)Fix speed infinite when sliding frame in recording mode + re-add speed in animation
(/)Remove rifle mode and optimise immersive mode in Presentation mode
(/)Fix confirm dialog when creating new or resetting
(/)Fix Viewbox that disappeared
(/)Fix senddata that is reversed after exporting and importing
(/)save staircase connections
(/)Fix recording to video
(/)Fix new blank diagram. There are still elements here and there
(/)Delete paths when not in animation
(/)Fix snap to grid
(/)Fix element name that comes back to center upon renaming
(/)Fix clicking multiple times on play pause etc (goes with playback improvement)
(/)Just clicking on item shouldn't set it's position
(/)Fix visibility issue. Invisible objects still appear when dragging animation
(/)Fix connected items visibility
(/)Shouldn't be able to delete segment which is part of animation
(/)We should use the center of the item for animation, not center of the group (when text is off, it messes up positions)
(/)<PERSON>vent from adding same sendData twice on same frame
(/)Shouldn't be able to delete wait frame
(/)Going to creation mode should stop animation
(/)Fix visibility when sliding animation and when going to creation mode
(/)Remove black circles when in creation mode
(/)Stop rifle when in creation
(/)Fix glitch when moving name
(/)fix sequence bar edition
(/)fix undo redo
(/)When clicking play from last frame, opacity of some elements are not reset back to invisible
(/)Fix rectangle selection that persists
(/)Fix deleting elements part of sequences
(/)Fix feedback on view snapshot, bg should be green
(/)Refresh element paths when adding / removing frames or actions
(/)Prevent from moving elements or animate view between frames
(/)Change appearance of non-send-data action, they need to be shorter to show it's spontaneous
(/)Implement delete frame
(/)Deleting frames from UI should set items at right position and redraw paths
(/)Fix bug when moving connected elements together, some edges don't move
(/)Add horizontal delimiters between timeline controls, sequences etc.
(/)Fix empty node name
(/)Fix timeline red marker position when going beyond screen width
(/)Change detach to immersive (reverse logic)
(/)Fix when view is not set at first frame. Immersiveing view zooms out forever
(/)Fix snap to grid
(/)Fix rectangle selector that starts behaving like the hand when switching mode or double clicking on a node for renaming
(/)Handle case where we don't set the view on first frame... what do we do
(/)Automatically set to creation mode when loading to avoid issues with presentation mode
(/)Fix extreme right border which is not grided
(/)Fix nodName resize when editing name from panel
(/)Fix waiting time before rifle
(/)Allow to move and animate multiple items at once
(/)Max height for timeline
(/)Make texts resizeable
(/)When stopping rifle mode, actually stop the loop. Currently if you play again just after stopping, it finishes the ones that started
(/)When loading a project, making a change and undoing, it duplicates texts... weird (didn't replicate)
(/)Fix New Project screen
(/)Fix Open Project screen
(/)Improve fps (and avoid negative values)
(/)Remove access to connectors in animation mode
(/)Fix issue with empty text on elements when moving
(/)fix recording quality
(/)There a failure with deleting frame, try to reproduce and fix
(/)Fix copy paste when font changed
(/)Fix removing multiple elements involved in an animation. We should ask for confirmation only once.
(/)Fix new example projects
(/)Fix tutorial
(/)Fix element blink (send data) when element opacity is not 100%
(/)Add confirmation for new project / handle autosave
(/)Force aspect ratio for images when resizing
()Fix rectangle selection in animation mode on non-first frame. It tries to select based on initial elements position
()Check that we handle asynchronousity everywhere for store.dispatch
()Maybe make everything visible in rifle mode



Refactor state to separate diagram from parameters
Check if svg list is a better solution for moving multiple objects
Rename anchor to connector

----------------FEATURES------------------
(/) Rifle mode
(/) Immersive from view in record mode
(/)non linear segments
(/)Other shapes
(/)Animate moving objects
(/)fix shapes using dummies
(/)fix import / export
(/)fix undo / redo
(/)Add resize icon for resize
(/)Better colors handling
(/)Make tabs to switch between sequences and elements
(/)Save in cookies (and propose to restore if we come back)
(/)Save Viewport
(/)Mode demo / tuto
(/)Make the bin to delete individual objects or everything depending on what's selected
(/)Add text
(/)Make better playback: pause actually pauses the animation + see intermediate frames (between frames)
(/)replace green and red dots by an eye to make items to appear or disappear
(/)Change font size for element names
(/)Improve player + add option to minimise
(/)Save view when in creation mode so that when we switch back, we land where we were before going to animation mode
(/)make it impossible to select or move objects when on intermediate frame
(/)Make a player version (another page?) to be included in confluence
(/)Transparent player / hide player / include player on the side toolbar
Make elements in creation mode to belong to a specific sequence (in case it becomes too messy)
Make bi-directional rifle mode possible
In rifle mode give ability to chose which elements will be visible or not
Mini rifles (specify how many black dots will travel during one frame)
--> Randomise mini rifles. The goal is to simulate randomness in a system
--> Keep states depending on received dots (count, color change etc.)
--> Animate label (see increasing counter)
Display placeholder for element when drag-creating
Add tooltips
Make a road trip demo (with a google map background and a car going along the road
Add preferences on top grey bar (snap to grid etc.)
Add load image from local files
Add shortcuts for everything and add it in the menus for description
Make entities as groups to allow more beautiful shapes
Save images added and display them underneath search bar for quick use. Also click on or drag to use

(?)Propose to edit text straight away after creating element
Generate reusable loop animations / templates -> market place
Explore doing actual 24 fps
Implement zoom in element function (workspaces)
Give ability to move anchor points
Animate colors
Animate size
Rotationo + animate
Other shapes for send data than black circle (soccer ball? soccer strategy)
(Keep in mind that this could work for electrical circuits or all sorts of other diagrams)
Change background (planet simulation with space background)


----------------ALPHA RELEASE------------------
--fixes--
(/)Fix blinking with transparent shapes
(/)Fix demo autoplay failing
(/)Fix tutorial
(/)Fix player
(/)Remove right panel sequences (keep it for updating dot color and frame speed)
(/)Fix Undo Redo
(/)Fix New project
(/)When getting into presentation mode, switch back to hand selection mode automatically, otherwise we're stuck
(/)When there is a dataflow set between 2 nodes and then we move them together in creation mode, when getting back to animation, it fails (related to pathkeys)
(/)Disable director's view if no view set
(/)Add validation for frame duration when there are no actions (like unused frames)
(/)Changing shadow or display grid in animation or playback displays anchors...
(/)Remove vue warnings
(/)Fix red marker in timeline not aligned when frame number gets high
(/)Fix icons alignements in menus (reintroduce v-list-icons?)
(?)Fix link expiry time
(/)Fix zoom out that gets stuck
(/)Remove effects section
(/)Fix interpolation for viewbox when removing frame
(/)Make landing page responsive
(/)Reduce hero text size for phone
(/)Getting to tutorial and exiting crashes
(?)Check why macos adds .json when saving
()Undo redo shouldn't reset preferences
()Fix video crop on one of videos

--core functionalities--
(/)Change dot color
(/)Change individual frame speed
(/)Clicking on element opens right sidebar unless we dismissed it once
(/)Add sequences in presentation and player
(/)Preferences menu
(/)Resume from autosave
(/)Highlight elements on hover in timeline
(/)make smlx the extension. Can't open files not in that format
(/)add version to the workspace
(/)add download button for video
(/)Add a select all in edit
(/)Confirmation messages before deleting
(/)Add feedback formS
(/)Disable for mobile
(/)Rate limit + control link sharing
(/)Add autoplay and display grid as parameters for player
(/)Allow player to open local file
(/)When setting frame duration, adding new actions should make their duration to the defaulted one
(/)Add default shapes and make it simpler in code - added ability to add local images
(/)Increase controls width for large frame numbers with decimal (currently going at new line)
(/)Update hardcoded 1920
(/)Add default to 1920 1080 when nothing specified
(/)Add hints and / or tour
Save undo redo when getting to tutorial and then restore on quit
Anonymise diagrams (just use uuid not name) -> more secure when sharing links

--cosmetics--
(/)Fix landing page
(/)Create videos
(/)Add player links on landing page (iframes??)
(/)Create demos
Add an about

--monitoring
Setup analytics to check users behaviour
Track issues


--workspaces
- bug fixes
(/)Fix right click menu
(/)Fix SetupObjectsAtFrame for zoom and imported actions
(/)Fix Next Frame / Previous frame
(/)Fix import sequence from other workspace
()Automatically collapse workspaces when going into animation
()Fix save load undo redo etc.
()Fix collapsing expanded workspaces. It should bring elements closer
(/)Fix collapsing workspace. It should land back to initial position, but currently shifted up a little bit
()Fix expanding workspaces. It should push other expanded workspaces away
()Fix paths displaying for all elements even not selected and also in presentation mode
()Update text size for elements when resizing expanded workspace
()Fix display shadow preferences
()Fix animation when inside a workspace
