import store from "@/store";

let fileHandle;
export const saveTypes = {
  filesystem: "FILESYSTEM",
  browser: "BROWSER",
};
export let saveDefault;

export async function saveFile(name, object) {
  const options = {
    suggestedName: name + ".smlx",
    types: [
      {
        description: "Json Files",
        accept: { "application/json": [".json"] },
      },
    ],
  };
  fileHandle = await window.showSaveFilePicker(options);
  await writeFile(JSON.stringify(object));
  return removeExtension(fileHandle?.name);
}

export async function updateFile(object) {
  if (fileHandle) {
    await writeFile(JSON.stringify(object));
  } else {
    await saveFile(store.state.diagramName, object);
  }
  return removeExtension(fileHandle?.name);
}

async function writeFile(content) {
  const writable = await fileHandle?.createWritable();
  await writable?.write(content);
  await writable?.close();
}

export const setFileHandle = (value) => {
  fileHandle = value;
};

export const resetFileHandle = () => {
  fileHandle = null;
};

export const setSaveDafault = (value) => {
  saveDefault = value;
};

export const resetSaveDefault = () => {
  saveDefault = null;
};

function removeExtension(filename) {
  return filename.replace(/\.[^/.]+$/, "");
}
