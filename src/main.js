import { createApp } from "vue";
import App from "./App.vue";
import store from "./store";
// Vuetify
import "vuetify/styles";
import { createRouter, createWebHistory } from "vue-router";
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import "@mdi/font/css/materialdesignicons.css";
import Home from "@/components/Home.vue";

const routes = [{ path: "/", component: Home }];

const router = createRouter({
  history: createWebHistory(),
  mode: "history",
  routes,
});

const vuetify = createVuetify({
  components,
  directives,
  icons: {
    defaultSet: "mdi",
  },
});
createApp(App).use(vuetify).use(store).use(router).mount("#app");
