import { draw } from "@/plugins/svgjs";
import { getGraphicShape } from "@/shapes";
import {
  setupAnchorsListeners,
  setupBackgroundListeners,
  setupEditableDivListeners,
  setupEditableElementNameListeners,
  setupElementEntityListeners,
  setupExpandedBoxListener,
  setupExpandIconListeners,
  setupGraphicElementListeners,
  setupSegmentListener,
  setupSvgContainerListener,
} from "@/listeners";
import { store } from "./store";
import { collapseWorkspace, expandWorkspace } from "@/core";

export const drawAdapter = {
  parameters: {
    allElements: new Map(),
    allSegments: new Map(),
    expandedWorkspaceBoxes: new Map(), // Maps element ID to expanded workspace box
    rectangleBackground: null,
    backgroundPattern: {},
    rectangleSelection: {},
    multiObjectsMoveInitialPosition: {},
    multiObjectsMoveCurrentPosition: {},
    draggingStarted: false,
    movingObject: false,
    dragStartFirstSelectedElementCoordinates: {},
    svgContainer: null,
    recorder: null,
    immersiveViewbox: null,
  },
  initialise: () => {
    if (drawAdapter.parameters.immersiveViewbox) {
      drawAdapter.parameters.immersiveViewbox.remove();
      drawAdapter.parameters.immersiveViewbox = null;
    }
    if (drawAdapter.parameters.svgContainer) {
      drawAdapter.parameters.svgContainer.remove();
    }
    drawAdapter.createContainer(
      "#svg-container",
      10000,
      10000,
      0.2,
      20,
      true,
      0.2,
    );
    setupSvgContainerListener(drawAdapter.parameters.svgContainer);
  },
  setup: (viewboxX, viewboxY, viewboxWidth, viewboxHeight) => {
    drawAdapter.createBackground(
      viewboxWidth,
      viewboxHeight,
      100,
      100,
      10,
      10,
      "#d5d2d2",
      "#f3ebeb",
    );
    drawAdapter.createViewbox(viewboxX, viewboxY, viewboxWidth, viewboxHeight);
    if (!drawAdapter.parameters.elementShadowFilter) {
      drawAdapter.parameters.elementShadowFilter =
        drawAdapter.createElementShadowFilter();
    }
    drawAdapter.parameters.elementShadowTextFilters = new Map();
  },
  createViewbox: (x, y, width, height) => {
    draw.viewbox(x, y, width, height);
  },
  createContainer: (
    containerName,
    width,
    height,
    zoomMin,
    zoomMax,
    panning,
    zoomFactor,
  ) => {
    drawAdapter.parameters.svgContainer = draw
      .addTo(containerName)
      .size(width, height)
      .panZoom({
        zoomMin,
        zoomMax,
        panning,
        zoomFactor,
        oneFingerPan: true,
      });
  },
  createBackground: (
    width,
    height,
    bigPatternWidth,
    bigPatternHeight,
    smallPatternWidth,
    smallPatternHeight,
    colorBigSquares,
    colorSmallSquares,
  ) => {
    if (drawAdapter.parameters.rectangleBackground) {
      drawAdapter.parameters.rectangleBackground.remove();
    }
    drawAdapter.parameters.rectangleBackground = draw
      .rect(width, height)
      .attr("data-testid", "background");
    if (!drawAdapter.parameters.immersiveViewbox) {
      drawAdapter.parameters.immersiveViewbox = draw
        .rect(0, 0)
        .opacity(0)
        .fill("none");
    }
    const smallSquaresPattern =
      drawAdapter.parameters.backgroundPattern.smallPattern ||
      draw.pattern(
        smallPatternWidth,
        smallPatternHeight,
        createSmallPattern("#fff", colorSmallSquares),
      );
    const pattern =
      drawAdapter.parameters.backgroundPattern.pattern ||
      draw.pattern(
        bigPatternWidth,
        bigPatternHeight,
        createBigPattern(colorBigSquares, smallSquaresPattern),
      );

    drawAdapter.parameters.backgroundPattern.smallPattern = smallSquaresPattern;
    drawAdapter.parameters.backgroundPattern.pattern = pattern;
    drawAdapter.parameters.rectangleBackground.fill(
      drawAdapter.parameters.backgroundPattern.pattern,
    );

    setupBackgroundListeners(drawAdapter.parameters.rectangleBackground);
    setupBackgroundListeners(drawAdapter.parameters.immersiveViewbox);
  },
  createElementShadowFilter: () => {
    return draw.filter(function (add) {
      // 1. Create an offset, blurred copy of the source alpha.
      const blur = add.offset(15, 15).gaussianBlur(5);

      // 2. Flood with light grey and then composite it with the blurred shape.
      // The composite with operator "in" means that the flood color is only visible
      // where the blur (i.e. the shadow shape) has opacity.
      const coloredShadow = add.flood("#727272").composite(blur, "in");

      // 3. Blend the original source with the colored shadow.
      add.blend(add.$source, coloredShadow);

      // Adjust the filter region if needed.
      this.size("200%", "200%").move("-50%", "-50%");
    });
  },
  createElementShadowTextFilter: (element) => {
    return draw.filter(function (add) {
      // 1. Create an offset, blurred copy of the source alpha.
      const blur = add.offset(1, 1).gaussianBlur(1);

      // 2. Flood with the element's fill color and composite it with the blurred alpha.
      const baseShadow = add
        .flood(element.graphicElement.elementName.fill())
        .composite(blur, "in");

      // 3. Invert the color using a color matrix.
      // The matrix below does: newColor = (-1 * oldColor) + 1 for R, G, and B.
      const invertedShadow = add
        .colorMatrix(
          "matrix",
          [-1, 0, 0, 0, 1, 0, -1, 0, 0, 1, 0, 0, -1, 0, 1, 0, 0, 0, 1, 0],
        )
        .in(baseShadow);

      // 4. Blend the original source with the inverted shadow.
      add.blend(add.$source, invertedShadow);

      // 5. Adjust the filter region if needed.
      this.size("200%", "200%").move("-50%", "-50%");
    });
  },
  createElement: (
    posX,
    posY,
    type,
    elementName,
    elementNameX,
    elementNameY,
    elementId,
    elementColor,
    textColor,
    textSize,
    textFont,
    textWeight,
    textAlign,
    width,
    height,
    anchors,
    displayShadows,
  ) => {
    width = width || (type === "TEXT-CLASSIC" ? 200 : 100);
    height = height || (type === "TEXT-CLASSIC" ? 50 : 100);
    textSize = textSize || 16;
    textFont = textFont || "Arial";
    textWeight = textWeight || "bold";
    textAlign = textAlign || "center";
    let element = {};
    element.id = elementId;
    element.name = elementName;
    element.type = type;
    drawAdapter.parameters.allElements.set(element.id, element);

    element.graphicElement = draw.group().draggable();

    element.graphicElement.dummy = element.graphicElement
      .rect(width, height)
      .center(posX, posY)
      .fill("#ffffff00");

    element.graphicElement.entity = getGraphicShape(element, type);
    element.graphicElement.entity
      .stroke({ color: "#000", width: 1 })
      .fill(elementColor);
    element.graphicElement.entity.size(width, height);
    element.graphicElement.entity.center(posX, posY);
    if (displayShadows) {
      element.graphicElement.entity.filterWith(
        drawAdapter.parameters.elementShadowFilter,
      );
    }

    let elementEntity = element.graphicElement.entity;

    elementEntity.color = elementEntity.attr("fill");

    element.graphicElement.dummyStroke = element.graphicElement
      .rect(width, height)
      .center(posX, posY)
      .fill("none")
      .stroke({ color: "#a9a6ff", width: 1, dasharray: 3 })
      .hide();

    drawAdapter.configureElementAnchors(element.id, anchors);

    textColor = textColor || (type === "TEXT-CLASSIC" ? "#000000" : "#ffffff");

    let editableDiv = document.createElement("div");
    editableDiv.setAttribute("contentEditable", "false");
    editableDiv.setAttribute("id", `editableName-${element.id}`);
    editableDiv.setAttribute(
      "style",
      `max-width: ${width - 10}px; overflow-wrap: break-word; font-weight: ${textWeight}; font-size: ${textSize}px; color: ${textColor}; font-family: ${textFont}, Helvetica, sans-serif; user-select: none; text-align: ${textAlign};`,
    );
    editableDiv.textContent = elementName;
    if (type === "TEXT-CLASSIC") {
      element.graphicElement.elementName = element.graphicElement
        .foreignObject(width - 10, height - 10)
        .attr("pointer-events", "none");
      element.graphicElement.elementName.add(editableDiv);
      element.graphicElement.elementName.x(
        elementNameX || element.graphicElement.entity.x() + 5,
      );
      element.graphicElement.elementName.y(
        elementNameY ||
          element.graphicElement.entity.cy() - editableDiv.offsetHeight / 2,
      );
      element.graphicElement.elementName.editableDiv = editableDiv;
      element.graphicElement.elementName.attr({
        height: editableDiv.offsetHeight,
      });
    } else {
      element.graphicElement.elementName = element.graphicElement
        .text(elementName)
        .fill(textColor);
      element.graphicElement.elementName
        .amove(
          elementNameX ||
            element.graphicElement.entity.x() +
              (element.graphicElement.entity.width() -
                element.graphicElement.elementName.bbox().width) /
                2,
          elementNameY || element.graphicElement.entity.cy(),
        )
        .attr("pointer-events", "none")
        .attr(
          "style",
          `user-select: none; overflow-wrap: break-word; font-weight: ${textWeight}; font-size: ${textSize}px; font-family: ${textFont}, Helvetica, sans-serif; text-align: ${textAlign};`,
        );
      if (displayShadows) {
        if (drawAdapter.parameters.elementShadowTextFilters.get(element.id)) {
          drawAdapter.parameters.elementShadowTextFilters
            .get(element.id)
            .remove();
        }
        drawAdapter.parameters.elementShadowTextFilters.set(
          element.id,
          drawAdapter.createElementShadowTextFilter(element),
        );
        element.graphicElement.elementName.filterWith(
          drawAdapter.parameters.elementShadowTextFilters.get(element.id),
        );
      }
    }
    if (element.name === "") {
      // I know this is weird, I would have preferred not creating it at all, but it's the only way I could get it to work without weird bug
      element.graphicElement.elementName.remove(); // ...And that's because having it removed is different from having it null
    }

    element.graphicElement.resizer = element.graphicElement.group().draggable();
    element.graphicElement.resizer
      .rect(15, 15)
      .x(posX + width / 2 - 15)
      .y(posY + height / 2 - 15)
      .fill("white")
      .opacity(0.9);
    element.graphicElement.resizer
      .path(
        "m 164.84149,202.40597 c -13.05994,-13.05995 -26.11989,-26.11989 -39.17983,-39.17983 -3.25368,3.25368 -6.50737,6.50737 -9.76106,9.76106 13.05995,13.05994 26.11989,26.11988 39.17983,39.17983 -4.2066,4.20687 -8.4132,8.41375 -12.6198,12.62062 11.66717,0 23.33432,0 35.00149,0 0,-11.66716 0,-23.33432 0,-35.00148 -4.20688,4.2066 -8.41375,8.4132 -12.62063,12.6198 z M 211.97288,47.631743 c -70.55556,0 -141.111108,0 -211.66666833,0 0,70.555557 0,141.111117 0,211.666667 70.55556033,0 141.11110833,0 211.66666833,0 0,-70.55555 0,-141.11111 0,-211.666667 z M 21.012752,150.47033 c 0,-27.37735 0,-54.754703 0,-82.132047 27.37735,0 54.754697,0 82.132038,0 -8.808138,8.808282 -17.616283,17.616563 -26.424427,26.424844 13.059945,13.059953 26.119877,26.119893 39.179827,39.179843 -9.7612,9.76119 -19.522389,19.52239 -29.283584,29.28358 -13.059945,-13.05994 -26.119884,-26.11988 -39.179834,-39.17983 -8.808,8.80787 -17.61601,17.61574 -26.42402,26.42361 z m 88.121548,88.12154 c 8.80814,-8.80828 17.61629,-17.61656 26.42443,-26.42484 -13.05995,-13.05995 -26.11989,-26.11989 -39.179825,-39.17983 9.761185,-9.7612 19.522385,-19.5224 29.283585,-29.28359 13.05994,13.05994 26.11988,26.11989 39.17983,39.17983 8.80814,-8.80801 17.61629,-17.61601 26.42443,-26.42402 0,27.37735 0,54.75469 0,82.13204 -27.37735,2.8e-4 -54.7558,-4.1e-4 -82.13245,4.1e-4 z M 96.378491,133.94297 C 83.318546,120.88302 70.258602,107.82308 57.198662,94.763127 c 4.2066,-4.206875 8.4132,-8.41375 12.6198,-12.620624 -11.66717,0 -23.33432,0 -35.00148,0 0,11.667161 0,23.334317 0,35.001487 4.20687,-4.2066 8.41375,-8.4132 12.62062,-12.6198 13.05995,13.05994 26.119886,26.11989 39.179831,39.17983 3.253685,-3.25368 6.507374,-6.50737 9.761058,-9.76105 z",
      )
      .x(posX + width / 2 - 15)
      .y(posY + height / 2 - 15)
      .width(15)
      .height(15)
      .opacity(0.9);
    element.graphicElement.resizer.size(15, 15);
    element.graphicElement.resizer.hide();

    if (type !== "TEXT-CLASSIC") {
      let nameX = element.graphicElement.elementName
        ? element.graphicElement.elementName.ax()
        : element.graphicElement.entity.cx();
      let nameY = element.graphicElement.elementName
        ? element.graphicElement.elementName.ay()
        : element.graphicElement.entity.cy();
      element.graphicElement.elementNameMover = element.graphicElement
        .path(
          "m-0.99,48.0125l22.5,-22.275l0,11.1375l16.25,0l0,-16.0875l-11.25,0l22.5,-22.275l22.5,22.275l-11.25,0l0,16.0875l16.25001,0l0,-11.1375l22.49999,22.275l-22.49999,22.275l0,-11.1375l-16.25001,0l0,16.08751l11.25,0l-22.5,22.27499l-22.5,-22.27499l11.25,0l0,-16.08751l-16.25,0l0,11.1375l-22.5,-22.275z",
        )
        .width(20)
        .height(20)
        .hide()
        .move(nameX, nameY - 40)
        .draggable()
        .attr("cursor", "grabbing");
    }

    if (type === "TEXT-CLASSIC") {
      const editableDiv = element.graphicElement.elementName.editableDiv;
      element.graphicElement.elementName.x(
        element.graphicElement.entity.x() + 5,
      );
      element.graphicElement.elementName.y(
        element.graphicElement.entity.y() + 5,
      );
      editableDiv.style.maxWidth = `${element.graphicElement.entity.width() - 10}px`;

      element.graphicElement.elementName.height(editableDiv.offsetHeight);

      element.graphicElement.entity.height(
        element.graphicElement.elementName.height() + 5,
      );
      element.graphicElement.dummy.height(
        element.graphicElement.elementName.height() + 5,
      );
      element.graphicElement.dummyStroke.height(
        element.graphicElement.elementName.height() + 5,
      );

      element.graphicElement.resizer.move(
        element.graphicElement.entity.x() + elementEntity.width() - 15,
        element.graphicElement.entity.y() + elementEntity.height() - 15,
      );
    }

    const elementCx = element.graphicElement.cx();
    const elementCy = element.graphicElement.cy();
    element.graphicElement.toggleDisplayOn = element.graphicElement
      .path(
        "m494.8,241.4l-50.6-49.4c-50.1-48.9-116.9-75.8-188.2-75.8s-138.1,26.9-188.2,75.8l-50.6,49.4c-11.3,12.3-4.3,25.4 0,29.2l50.6,49.4c50.1,48.9 116.9,75.8 188.2,75.8s138.1-26.9 188.2-75.8l50.6-49.4c4-3.8 11.7-16.4 0-29.2zm-238.8,84.4c-38.5,0-69.8-31.3-69.8-69.8 0-38.5 31.3-69.8 69.8-69.8 38.5,0 69.8,31.3 69.8,69.8 0,38.5-31.3,69.8-69.8,69.8zm-195.3-69.8l35.7-34.8c27-26.4 59.8-45.2 95.7-55.4-28.2,20.1-46.6,53-46.6,90.1 0,37.1 18.4,70.1 46.6,90.1-35.9-10.2-68.7-29-95.7-55.3l-35.7-34.7zm355,34.8c-27,26.3-59.8,45.1-95.7,55.3 28.2-20.1 46.6-53 46.6-90.1 0-37.2-18.4-70.1-46.6-90.1 35.9,10.2 68.7,29 95.7,55.4l35.6,34.8-35.6,34.7z",
      )
      .width(20)
      .height(13)
      .fill("#ffb0bc")
      .stroke({ color: "#000", width: 1 })
      .attr({ cursor: "pointer" })
      .draggable()
      .hide();

    element.graphicElement.toggleDisplayOff = element.graphicElement
      .path(
        "M294.908,142.225c-0.566-0.756-14.168-18.72-38.881-36.693c-10.007-7.277-20.418-13.504-31.116-18.652l47.458-47.458 c4.084-4.084,4.084-10.706,0-14.79c-4.085-4.083-10.705-4.083-14.79,0L203.922,78.29c-18.06-6.122-36.7-9.269-55.42-9.269 c-37.501,0-74.683,12.625-107.526,36.51C16.262,123.506,2.658,141.47,2.092,142.225c-2.789,3.718-2.789,8.831,0,12.549 c0.566,0.756,14.17,18.72,38.884,36.694c10.006,7.277,20.418,13.503,31.115,18.651l-47.458,47.458 c-4.084,4.084-4.084,10.706,0,14.79c2.043,2.042,4.719,3.063,7.394,3.063c2.678,0,5.354-1.021,7.396-3.063l53.658-53.658 c18.062,6.122,36.701,9.268,55.421,9.268c37.502,0,74.684-12.625,107.525-36.511c24.713-17.974,38.315-35.938,38.881-36.693 C297.697,151.057,297.697,145.943,294.908,142.225z M207.065,148.5c0,32.292-26.271,58.564-58.563,58.564 c-12.376,0-23.859-3.87-33.328-10.446l23.981-23.98c2.899,1.123,6.05,1.746,9.347,1.746c14.296,0,25.883-11.587,25.883-25.883 c0-3.298-0.623-6.447-1.746-9.348l23.98-23.98C203.196,124.641,207.065,136.123,207.065,148.5z M89.939,148.5 c0-32.292,26.271-58.563,58.564-58.563c12.376,0,23.859,3.868,33.326,10.446l-23.98,23.98c-2.9-1.123-6.049-1.746-9.346-1.746 c-14.296,0-25.883,11.587-25.883,25.883c0,3.297,0.623,6.446,1.746,9.346l-23.98,23.98C93.808,172.358,89.939,160.876,89.939,148.5z M24.153,148.5c5.687-6.283,15.785-16.427,29.681-26.457c9.118-6.581,18.458-12.157,27.996-16.725 c-8.088,12.443-12.807,27.268-12.807,43.182s4.719,30.738,12.807,43.182c-9.538-4.567-18.878-10.144-27.996-16.725 C39.937,164.925,29.836,154.779,24.153,148.5z M243.167,174.957c-9.115,6.581-18.456,12.156-27.991,16.724 c8.086-12.442,12.805-27.268,12.805-43.181s-4.719-30.738-12.805-43.181c9.535,4.567,18.876,10.143,27.991,16.724 c13.897,10.032,23.998,20.178,29.681,26.457C267.162,154.783,257.063,164.927,243.167,174.957z",
      )
      .width(20)
      .height(20)
      .x(elementCx + element.graphicElement.entity.width() / 4)
      .y(elementCy + element.graphicElement.entity.height() / 2 + 5)
      .fill("#ffc3a0")
      .stroke({ color: "#000", width: 1 })
      .attr({ cursor: "pointer" })
      .draggable()
      .hide();

    if (element.name !== "") {
      setupEditableElementNameListeners(
        element.graphicElement.elementName,
        element,
      );
    }

    setupElementEntityListeners(elementEntity, element);
    setupEditableDivListeners(editableDiv, element);
    setupGraphicElementListeners(element, elementEntity);

    return element;
  },
  setPosition: (itemId, cx, cy) => {
    const item = drawAdapter.parameters.allElements.get(itemId);
    const entityOffsetX =
      item.graphicElement.cx() - item.graphicElement.entity.cx();
    const entityOffsetY =
      item.graphicElement.cy() - item.graphicElement.entity.cy();
    item.graphicElement
      // .animate(50)
      .center(cx + entityOffsetX, cy + entityOffsetY);
    return item;
  },
  highlightElement: (elementId, displayExpandIcon) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    const graphicElement = element.graphicElement;
    const elementEntity = graphicElement.entity;
    const resizer = graphicElement.resizer;
    elementEntity.opacity(0.9).stroke({ color: "#000", width: 3 });
    elementEntity.topAnchor.animate({ duration: 100, when: "now" }).size(7);
    elementEntity.leftAnchor.animate({ duration: 100, when: "now" }).size(7);
    elementEntity.bottomAnchor.animate({ duration: 100, when: "now" }).size(7);
    elementEntity.rightAnchor.animate({ duration: 100, when: "now" }).size(7);
    graphicElement.dummyStroke
      .center(elementEntity.cx(), elementEntity.cy())
      .show();
    resizer
      .x(elementEntity.x() + elementEntity.width() - 15)
      .y(elementEntity.y() + elementEntity.height() - 15)
      .show();
    resizer.opacity(0.9);

    // create expand icon helper
    // Create invisible helper rectangle for hover detection
    if (!element.graphicElement.hoverHelper) {
      const helperWidth = element.graphicElement.entity.width();
      const helperHeight = 30; // Height of the hover area above the element
      const helperX = element.graphicElement.entity.x();
      const helperY = element.graphicElement.entity.y() - helperHeight;

      element.graphicElement.hoverHelper = element.graphicElement
        .rect(helperWidth, helperHeight)
        .move(helperX, helperY)
        .fill("transparent")
        .opacity(0) // Completely invisible
        .attr({ cursor: "default" });
    }

    if (displayExpandIcon) {
      // Create expand icon with a more modern design (hidden by default)
      if (!element.graphicElement.expandIconGroup) {
        const expandIconX = element.graphicElement.entity.x();
        const expandIconY = element.graphicElement.entity.y() - 22;

        // Create a group for the expand icon
        element.graphicElement.expandIconGroup = element.graphicElement.group();

        // Create circular background
        element.graphicElement.expandIconBg =
          element.graphicElement.expandIconGroup
            .circle(16)
            .center(expandIconX + 8, expandIconY + 8)
            .fill("#f5f5f5")
            .stroke({ color: "#3c4365", width: 1.5, opacity: 0.8 })
            .attr({ cursor: "pointer" });

        // Create dots icon (three dots menu icon)
        element.graphicElement.expandIconDot1 =
          element.graphicElement.expandIconGroup
            .circle(2.5)
            .center(expandIconX + 8, expandIconY + 4)
            .fill("#000000");

        element.graphicElement.expandIconDot2 =
          element.graphicElement.expandIconGroup
            .circle(2.5)
            .center(expandIconX + 8, expandIconY + 8)
            .fill("#000000");

        element.graphicElement.expandIconDot3 =
          element.graphicElement.expandIconGroup
            .circle(2.5)
            .center(expandIconX + 8, expandIconY + 12)
            .fill("#000000");

        // Set the expand icon reference to the group
        element.graphicElement.expandIcon =
          element.graphicElement.expandIconGroup;

        setupExpandIconListeners(element);
      }

      // Add expand workspace button on top right if the element has a workspace
      const simpleElement = store.state.allSimpleElements.get(element.id);
      if (
        simpleElement &&
        simpleElement.childWorkspaceId &&
        !element.graphicElement.workspaceExpandBtn
      ) {
        // Create expand workspace button on top right
        const expandBtnX =
          element.graphicElement.entity.x() +
          element.graphicElement.entity.width() -
          10;
        const expandBtnY = element.graphicElement.entity.y() - 14;

        // Create button group
        element.graphicElement.workspaceExpandBtnGroup =
          element.graphicElement.group();

        // Create circular background
        element.graphicElement.workspaceExpandBtnBg =
          element.graphicElement.workspaceExpandBtnGroup
            .circle(16)
            .center(expandBtnX, expandBtnY)
            .fill("#f5f5f5")
            .stroke({ color: "#3c4365", width: 1.5, opacity: 0.8 })
            .attr({ cursor: "pointer" });

        // Create expand/collapse icon
        element.graphicElement.workspaceExpandBtn =
          element.graphicElement.workspaceExpandBtnGroup
            .path(
              "M 4 9 V 4 h 5 L 7 6 L 11 10 L 10 11 L 6 7 L 4 9 M 20 4 L 15 4 L 17 6 L 13 10 L 14 11 L 18 7 L 20 9 L 20 4 M 4 20 L 4 15 L 6 17 L 10 13 L 11 14 L 7 18 L 9 20 L 4 20 M 4 4 M 20 15 v 5 h -5 L 17 18 L 13 14 L 14 13 L 18 17 L 20 15 M 20 20",
            )
            .fill("#3c4365")
            .attr({ cursor: "pointer" })
            .size(10, 10);

        element.graphicElement.workspaceExpandBtn.center(
          expandBtnX,
          expandBtnY,
        );

        // Setup listeners for the expand workspace button
        element.graphicElement.workspaceExpandBtnGroup
          .on("click", async () => {
            const simpleElement = store.state.allSimpleElements.get(element.id);
            if (simpleElement && simpleElement.childWorkspaceId) {
              if (store.getters.expandedWorkspaces.has(element.id)) {
                await collapseWorkspace(element.id);
              } else {
                await expandWorkspace(element.id);
              }
            }
          })
          .on("mouseenter", () => {
            element.graphicElement.workspaceExpandBtn
              .animate(200)
              .fill("#1976d2");
          })
          .on("mouseleave", () => {
            element.graphicElement.workspaceExpandBtn
              .animate(200)
              .fill("#3c4365");
          });
      }
    }
  },
  highlightSegment: (segmentId) => {
    const segment = drawAdapter.parameters.allSegments.get(segmentId);
    segment.graphicSegment.stroke({
      color: "#000",
      width: 4,
    });
  },
  showItemPositions: (elementId, positions) => {
    if (!positions) {
      return;
    }
    const element = drawAdapter.parameters.allElements.get(elementId);
    element.pathKeys = element.pathKeys || [];
    element.pathLines = element.pathLines || [];
    positions.forEach((position, index) => {
      if (index < positions.length - 1) {
        const nextIndex = positions[index + 1];
        if (nextIndex && nextIndex.x && nextIndex.y) {
          element.pathLines[index] =
            element.pathLines[index] ||
            draw.line(position.x, position.y, nextIndex.x, nextIndex.y).stroke({
              color: "#a5a5a5",
              width: 3,
              dasharray: 3,
            });
          element.pathLines[index].plot(
            position.x,
            position.y,
            nextIndex.x,
            nextIndex.y,
          );
        }
      }
      let keyColor = position.interpolated ? "grey" : "green";
      let keySize = position.interpolated ? 5 : 10;
      element.pathKeys[index] =
        element.pathKeys[index] ||
        draw.circle(keySize).center(position.x, position.y).fill(keyColor);
      element.pathKeys[index].center(position.x, position.y);
    });
  },
  hideItemPositions: (elementId) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    if (!element) {
      return;
    }
    element.pathKeys?.forEach((key) => {
      key.remove();
    });
    element.pathLines?.forEach((line) => {
      line.remove();
    });
    element.pathKeys = [];
    element.pathLines = [];
  },
  configureElementAnchors: (elementId, anchors) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    if (!element) {
      return;
    }
    const elementEntity = element.graphicElement.entity;
    if (elementEntity.topAnchor) {
      elementEntity.topAnchor.remove();
    }
    if (elementEntity.bottomAnchor) {
      elementEntity.bottomAnchor.remove();
    }
    if (elementEntity.leftAnchor) {
      elementEntity.leftAnchor.remove();
    }
    if (elementEntity.rightAnchor) {
      elementEntity.rightAnchor.remove();
    }
    elementEntity.topAnchor = configureAnchor(element).center(
      anchors.top.x,
      anchors.top.y,
    );
    elementEntity.bottomAnchor = configureAnchor(element).center(
      anchors.bottom.x,
      anchors.bottom.y,
    );
    elementEntity.leftAnchor = configureAnchor(element).center(
      anchors.left.x,
      anchors.left.y,
    );
    elementEntity.rightAnchor = configureAnchor(element).center(
      anchors.right.x,
      anchors.right.y,
    );
  },
  unlightElement: (elementId) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    if (!element) {
      return;
    }
    const graphicElement = element.graphicElement;
    const elementEntity = graphicElement.entity;
    const resizer = graphicElement.resizer;
    elementEntity.opacity(1).stroke({ color: "#000", width: 1 });
    elementEntity.topAnchor.animate({ duration: 100, when: "now" }).size(5);
    elementEntity.leftAnchor.animate({ duration: 100, when: "now" }).size(5);
    elementEntity.bottomAnchor.animate({ duration: 100, when: "now" }).size(5);
    elementEntity.rightAnchor.animate({ duration: 100, when: "now" }).size(5);
    graphicElement.dummyStroke.hide();
    resizer.hide();

    // delete expand icon and helper
    element.graphicElement.expandIcon?.remove();
    element.graphicElement.hoverHelper?.remove();
    element.graphicElement.expandIconDot1?.remove();
    element.graphicElement.expandIconDot2?.remove();
    element.graphicElement.expandIconDot3?.remove();
    element.graphicElement.expandIconGroup?.remove();
    element.graphicElement.expandIcon = null;
    element.graphicElement.hoverHelper = null;
    element.graphicElement.expandIconDot1 = null;
    element.graphicElement.expandIconDot2 = null;
    element.graphicElement.expandIconDot3 = null;
    element.graphicElement.expandIconGroup = null;

    // delete workspace expand button
    element.graphicElement.workspaceExpandBtn?.remove();
    element.graphicElement.workspaceExpandBtnBg?.remove();
    element.graphicElement.workspaceExpandBtnGroup?.remove();
    element.graphicElement.workspaceExpandBtn = null;
    element.graphicElement.workspaceExpandBtnBg = null;
    element.graphicElement.workspaceExpandBtnGroup = null;
  },

  hideElementNameMover: (elementId) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    if (!element) {
      return;
    }
    element.graphicElement.elementNameMover.hide();
  },

  unselectSegment: (segmentId) => {
    const segment = drawAdapter.parameters.allSegments.get(segmentId);
    if (!segment) {
      return;
    }
    segment.graphicSegment.stroke({
      color: "#000",
      width: 2,
    });
  },
  updateGraphicElementFromState: (
    simpleElement,
    displayShadows,
    currentWorkspaceId,
  ) => {
    drawAdapter.deleteElement(simpleElement.id);
    drawAdapter.createElement(
      simpleElement.workspaceParameters[currentWorkspaceId].x,
      simpleElement.workspaceParameters[currentWorkspaceId].y,
      simpleElement.type,
      simpleElement.name,
      simpleElement.textX,
      simpleElement.textY,
      simpleElement.id,
      simpleElement.color,
      simpleElement.textColor,
      simpleElement.textSize,
      simpleElement.textFont,
      simpleElement.textWeight,
      simpleElement.textAlign,
      simpleElement.width,
      simpleElement.height,
      simpleElement.workspaceParameters[currentWorkspaceId].anchors,
      displayShadows,
    );
  },
  getGraphicElementProperties: (elementId) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    if (!element) {
      return;
    }
    return {
      x: element.graphicElement.entity.cx(),
      y: element.graphicElement.entity.cy(),
      width: element.graphicElement.entity.width(),
      height: element.graphicElement.entity.height(),
    };
  },
  changeBackground: (color, colorSmallSquares, colorBigSquares, blank) => {
    if (blank) {
      drawAdapter.parameters.rectangleBackground.fill("#fff");
      return;
    }
    if (color) {
      drawAdapter.parameters.backgroundPattern.smallPattern.update(
        createSmallPattern(color, colorSmallSquares, colorBigSquares),
      );
    } else {
      drawAdapter.parameters.backgroundPattern.smallPattern.update(
        createSmallPattern(),
      );
    }
    drawAdapter.parameters.rectangleBackground.fill(
      drawAdapter.parameters.backgroundPattern.pattern,
    );
    drawAdapter.parameters.rectangleBackground.back();
  },
  traceSegment: (
    segmentId,
    mode,
    connectedAnchors,
    expandedWorkspaceId,
    offsetPosition,
  ) => {
    const segment = drawAdapter.parameters.allSegments.get(segmentId) || {
      id: segmentId,
    };
    if (segment.graphicSegment) {
      segment.graphicSegment.remove();
    }

    let path;
    if (mode === "LINEAR") {
      path = `M ${connectedAnchors.anchorFrom.x} ${connectedAnchors.anchorFrom.y} L ${connectedAnchors.anchorTo.x} ${connectedAnchors.anchorTo.y}`;
    } else {
      if (connectedAnchors.orientation === "horizontal") {
        // Horizontal connection (left to right or right to left)
        path = `M ${connectedAnchors.anchorFrom.x} ${connectedAnchors.anchorFrom.y} h ${(connectedAnchors.anchorTo.x - connectedAnchors.anchorFrom.x) / 2} v ${connectedAnchors.anchorTo.y - connectedAnchors.anchorFrom.y} h ${(connectedAnchors.anchorTo.x - connectedAnchors.anchorFrom.x) / 2}`;
      } else if (connectedAnchors.orientation === "vertical") {
        // Vertical connection (top to bottom or bottom to top)
        path = `M ${connectedAnchors.anchorFrom.x} ${connectedAnchors.anchorFrom.y} v ${(connectedAnchors.anchorTo.y - connectedAnchors.anchorFrom.y) / 2} h ${connectedAnchors.anchorTo.x - connectedAnchors.anchorFrom.x} v ${(connectedAnchors.anchorTo.y - connectedAnchors.anchorFrom.y) / 2}`;
      } else {
        // Diagonal connection (top to right, top to left, bottom to right, bottom to left, etc.)
        // For diagonal connections, we need to ensure the path is perpendicular to the connecting nodes

        // Determine if the source anchor is on a vertical side (top/bottom)
        // or a horizontal side (left/right) of the element
        const isSourceVertical = isSourceAnchorVertical(connectedAnchors);

        // For horizontal anchors (left/right), the path should start horizontally
        // For vertical anchors (top/bottom), the path should start vertically
        const isSourceHorizontal = !isSourceVertical;

        // Choose the path based on the source anchor type
        if (isSourceHorizontal) {
          // If source is left or right, go horizontal first then vertical
          path = `M ${connectedAnchors.anchorFrom.x} ${connectedAnchors.anchorFrom.y}h ${connectedAnchors.anchorTo.x - connectedAnchors.anchorFrom.x} v ${connectedAnchors.anchorTo.y - connectedAnchors.anchorFrom.y}`;
        } else {
          // If source is top or bottom, go vertical first then horizontal
          path = `M ${connectedAnchors.anchorFrom.x} ${connectedAnchors.anchorFrom.y} v ${connectedAnchors.anchorTo.y - connectedAnchors.anchorFrom.y} h ${connectedAnchors.anchorTo.x - connectedAnchors.anchorFrom.x}`;
        }
      }
    }
    segment.graphicSegment = draw.path(path).fill("none").stroke({
      color: "#000",
      width: 2,
    });
    if (offsetPosition) {
      segment.graphicSegment.dmove(offsetPosition.x, offsetPosition.y);
    }
    setupSegmentListener(segment);
    const expandedWorkspaceBox =
      drawAdapter.parameters.expandedWorkspaceBoxes.get(expandedWorkspaceId);
    if (expandedWorkspaceBox) {
      expandedWorkspaceBox.add(segment.graphicSegment);
      segment.graphicSegment.off();
    }
    drawAdapter.parameters.allSegments.set(segment.id, segment);
  },
  deleteElement: (elementId, deregister) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    if (!element) {
      return;
    }
    element.graphicElement.elementName?.remove();
    element.graphicElement.remove();
    element.pathKeys?.forEach((pathKey) => pathKey.remove());
    element.pathLines?.forEach((pathLine) => pathLine.remove());
    drawAdapter.parameters.elementShadowTextFilters.get(elementId)?.remove();
    drawAdapter.parameters.elementShadowTextFilters.set(elementId, null);
    if (deregister) {
      drawAdapter.parameters.allElements.delete(elementId);
    }
  },
  deleteAllElements: () => {
    drawAdapter.parameters.allElements.forEach((element) => {
      drawAdapter.deleteElement(element.id, true);
    });
    drawAdapter.parameters.allElements.clear();
  },
  deleteSegment: (segmentId) => {
    const segment = drawAdapter.parameters.allSegments.get(segmentId);
    segment?.graphicSegment.remove();
    drawAdapter.parameters.allSegments.delete(segmentId);
  },
  deleteAllSegments: () => {
    drawAdapter.parameters.allSegments.forEach((segment) => {
      drawAdapter.deleteSegment(segment.id);
    });
    drawAdapter.parameters.allSegments.clear();
  },
  resetCursors: () => {
    drawAdapter.parameters.allElements.forEach((element) => {
      element.graphicElement.attr({ cursor: "default" });
      element.graphicElement.entity.attr({ cursor: "default" });
      element.graphicElement.entity.topAnchor.attr({ cursor: "default" });
      element.graphicElement.entity.rightAnchor.attr({ cursor: "default" });
      element.graphicElement.entity.bottomAnchor.attr({ cursor: "default" });
      element.graphicElement.entity.leftAnchor.attr({ cursor: "default" });
    });
    drawAdapter.parameters.allSegments.forEach((segment) => {
      segment.graphicSegment.attr({ cursor: "default" });
    });
  },
  blinkValidation: (elementId) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    element.graphicElement.entity.stroke({ width: 4 });
    element.graphicElement.entity
      .animate(50)
      .stroke({ color: "#22ff00" })
      .animate()
      .stroke({ color: "#000" })
      .after(() => {
        // element.graphicElement.entity.stroke({width: 3})
      });
  },

  showDisplayOnToggle: (elementId) => {
    let element = drawAdapter.parameters.allElements.get(elementId);
    const elementCx = element.graphicElement.entity.cx();
    const elementCy = element.graphicElement.entity.cy();
    element.graphicElement.toggleDisplayOn
      .size(20, 12)
      .move(
        elementCx + element.graphicElement.entity.width() / 4,
        elementCy + element.graphicElement.entity.height() / 2 + 9,
      )
      .fill("#c4ffb0")
      .stroke({ color: "#000", width: 1 })
      .attr({ cursor: "pointer" })
      .show();
    drawAdapter.parameters.allElements
      .get(elementId)
      .graphicElement.toggleDisplayOff.hide();
  },
  showDisplayOffToggle: (elementId) => {
    let element = drawAdapter.parameters.allElements.get(elementId);
    const elementCx = element.graphicElement.entity.cx();
    const elementCy = element.graphicElement.entity.cy();
    element.graphicElement.toggleDisplayOff
      .move(
        elementCx + element.graphicElement.entity.width() / 4,
        elementCy + element.graphicElement.entity.height() / 2 + 5,
      )
      .fill("#ffc3a0")
      .stroke({ color: "#000", width: 1 })
      .attr({ cursor: "pointer" })
      .size(20, 20)
      .show();
    drawAdapter.parameters.allElements
      .get(elementId)
      .graphicElement.toggleDisplayOn.hide();
  },
  showVisibilityToggles: () => {
    drawAdapter.parameters.allElements.forEach((element) => {
      const elementCx = element.graphicElement.cx();
      const elementCy = element.graphicElement.cy();

      element.graphicElement.toggleDisplayOn
        .move(
          elementCx + element.graphicElement.entity.width() / 4,
          elementCy + element.graphicElement.entity.height() / 2 + 5,
        )
        .fill("#c4ffb0")
        .stroke({ color: "#000", width: 1 })
        .attr({ cursor: "pointer" })
        .draggable()
        .show();

      element.graphicElement.toggleDisplayOff
        .move(
          elementCx + element.graphicElement.entity.width() / 4,
          elementCy + element.graphicElement.entity.height() / 2 + 5,
        )
        .fill("#ffc3a0")
        .stroke({ color: "#000", width: 1 })
        .attr({ cursor: "pointer" })
        .draggable()
        .show();
    });
  },
  hideVisibilityToggles: () => {
    drawAdapter.parameters.allElements.forEach((element) => {
      if (element.graphicElement.toggleDisplayOn) {
        element.graphicElement.toggleDisplayOn.hide();
      }
      if (element.graphicElement.toggleDisplayOff) {
        element.graphicElement.toggleDisplayOff.hide();
      }
    });
  },
  setPanning: (value) => {
    drawAdapter.parameters.svgContainer.panZoom({
      zoomMin: 0.2,
      zoomMax: 20,
      panning: value,
      zoomFactor: 0.5,
    });
  },
  resetEntities: () => {
    drawAdapter.parameters.allElements.forEach((element) => {
      element.graphicElement.animate({ duration: 100, when: "now" }).opacity(1);
    });
    drawAdapter.parameters.allSegments.forEach((segment) => {
      segment.graphicSegment.animate({ duration: 100, when: "now" }).opacity(1);
    });
    drawAdapter.hideElementPaths();
  },
  hideElementPaths: () => {
    drawAdapter.parameters.allElements.forEach((element) => {
      drawAdapter.hideItemPositions(element.id);
    });
  },
  hideImmersiveViewbox: () => {
    drawAdapter.parameters.immersiveViewbox.opacity(0);
  },

  /**
   * Creates a dotted box to represent an expanded workspace
   * @param {Object} expandedData - Data about the expanded workspace
   * @param {Array} childElements - Elements to create in the expanded workspace box
   * @param {Object} position - Center position of the expanded workspace
   */
  createExpandedWorkspaceBoxWithChildElements: (
    expandedData,
    childElements,
    position,
  ) => {
    const { elementId, width, height, centerX, centerY, workspaceId } =
      expandedData;

    const expandedWorkspaceBox = {};
    expandedWorkspaceBox.elementId = elementId;
    expandedWorkspaceBox.workspaceId = workspaceId;
    // Create a group for the expanded workspace box
    const boxGroup = draw.group().draggable();

    const simpleElement = store.state.allSimpleElements.get(elementId);

    expandedWorkspaceBox.graphicWorkspaceBox = boxGroup;
    // Create the dotted box

    const box = boxGroup
      .rect(width, height)
      .center(centerX, centerY)
      .fill(simpleElement.color.substring(0, 7) + "60")
      .stroke({ color: "#3c4365", width: 1, dasharray: "5,5" });

    boxGroup.box = box;

    const elementName = simpleElement ? simpleElement.name : "Workspace";

    // Create the workspace name label at the top of the box
    boxGroup.name = boxGroup
      .text(elementName)
      .font({ family: "Arial", size: 14, weight: "bold" })
      .fill("#3c4365")
      .move(box.x() + 10, box.y() - 20);

    // Create collapse button group
    boxGroup.collapseButtonGroup = boxGroup.group();

    // Create circular background for collapse button
    boxGroup.collapseButtonBg = boxGroup.collapseButtonGroup
      .circle(18)
      .center(box.x() + width - 18, box.y() - 10)
      .fill("#f5f5f5")
      .stroke({ color: "#3c4365", width: 1.5, opacity: 0.8 });

    // Create collapse icon (minus)
    boxGroup.collapseButton = boxGroup.collapseButtonGroup
      .path(
        "M 23 23 L 23 28 L 25 26 L 28 29 L 29 28 L 26 25 L 28 23 L 23 23 M 19 23 L 14 23 L 16 25 L 13 28 L 14 29 L 17 26 L 19 28 L 19 23 M 23 19 L 28 19 L 26 17 L 29 14 L 28 13 L 25 16 L 23 14 L 23 19 M 19 15 v 4 h -5 L 16 17 L 13 14 L 14 13 L 17 16 L 19 14 L 19 15 M 19 19",
      ) // Minus icon for collapse
      .fill("#3c4365")
      .size(12, 12);

    boxGroup.collapseButton.center(
      boxGroup.collapseButtonBg.cx(),
      boxGroup.collapseButtonBg.cy(),
    );

    // Create resizer for the expanded box
    boxGroup.resizer = boxGroup.group().draggable();
    boxGroup.resizer
      .rect(15, 15)
      .x(centerX + width / 2 - 15)
      .y(centerY + height / 2 - 15)
      .fill("white")
      .opacity(0.9);
    boxGroup.resizer
      .path(
        "m 164.84149,202.40597 c -13.05994,-13.05995 -26.11989,-26.11989 -39.17983,-39.17983 -3.25368,3.25368 -6.50737,6.50737 -9.76106,9.76106 13.05995,13.05994 26.11989,26.11988 39.17983,39.17983 -4.2066,4.20687 -8.4132,8.41375 -12.6198,12.62062 11.66717,0 23.33432,0 35.00149,0 0,-11.66716 0,-23.33432 0,-35.00148 -4.20688,4.2066 -8.41375,8.4132 -12.62063,12.6198 z M 211.97288,47.631743 c -70.55556,0 -141.111108,0 -211.66666833,0 0,70.555557 0,141.111117 0,211.666667 70.55556033,0 141.11110833,0 211.66666833,0 0,-70.55555 0,-141.11111 0,-211.666667 z M 21.012752,150.47033 c 0,-27.37735 0,-54.754703 0,-82.132047 27.37735,0 54.754697,0 82.132038,0 -8.808138,8.808282 -17.616283,17.616563 -26.424427,26.424844 13.059945,13.059953 26.119877,26.119893 39.179827,39.179843 -9.7612,9.76119 -19.522389,19.52239 -29.283584,29.28358 -13.059945,-13.05994 -26.119884,-26.11988 -39.179834,-39.17983 -8.808,8.80787 -17.61601,17.61574 -26.42402,26.42361 z m 88.121548,88.12154 c 8.80814,-8.80828 17.61629,-17.61656 26.42443,-26.42484 -13.05995,-13.05995 -26.11989,-26.11989 -39.179825,-39.17983 9.761185,-9.7612 19.522385,-19.5224 29.283585,-29.28359 13.05994,13.05994 26.11988,26.11989 39.17983,39.17983 8.80814,-8.80801 17.61629,-17.61601 26.42443,-26.42402 0,27.37735 0,54.75469 0,82.13204 -27.37735,2.8e-4 -54.7558,-4.1e-4 -82.13245,4.1e-4 z M 96.378491,133.94297 C 83.318546,120.88302 70.258602,107.82308 57.198662,94.763127 c 4.2066,-4.206875 8.4132,-8.41375 12.6198,-12.620624 -11.66717,0 -23.33432,0 -35.00148,0 0,11.667161 0,23.334317 0,35.001487 4.20687,-4.2066 8.41375,-8.4132 12.62062,-12.6198 13.05995,13.05994 26.119886,26.11989 39.179831,39.17983 3.253685,-3.25368 6.507374,-6.50737 9.761058,-9.76105 z",
      )
      .x(centerX + width / 2 - 15)
      .y(centerY + height / 2 - 15)
      .width(15)
      .height(15)
      .opacity(0.9);
    boxGroup.resizer.size(15, 15);

    // Create anchors for the expanded box
    // We use the same anchor positions as regular elements
    boxGroup.entity = box; // Set the entity reference for anchor positioning

    // Calculate anchor positions based on the box dimensions
    const boxAnchors = {
      top: { x: centerX, y: centerY - height / 2 },
      bottom: { x: centerX, y: centerY + height / 2 },
      left: { x: centerX - width / 2, y: centerY },
      right: { x: centerX + width / 2, y: centerY },
    };

    // Create the anchor circles
    boxGroup.topAnchor = configureAnchor(expandedWorkspaceBox).center(
      boxAnchors.top.x,
      boxAnchors.top.y,
    );

    boxGroup.bottomAnchor = configureAnchor(expandedWorkspaceBox).center(
      boxAnchors.bottom.x,
      boxAnchors.bottom.y,
    );

    boxGroup.leftAnchor = configureAnchor(expandedWorkspaceBox).center(
      boxAnchors.left.x,
      boxAnchors.left.y,
    );

    boxGroup.rightAnchor = configureAnchor(expandedWorkspaceBox).center(
      boxAnchors.right.x,
      boxAnchors.right.y,
    );

    // Note: Anchors are now stored in state, not in the graphics layer

    for (const childElement of childElements) {
      // Create the element in the current workspace
      const element = drawAdapter.createElement(
        childElement.workspaceParameters[workspaceId].x,
        childElement.workspaceParameters[workspaceId].y,
        childElement.type,
        childElement.name,
        childElement.workspaceParameters[workspaceId].textX,
        childElement.workspaceParameters[workspaceId].textY,
        childElement.id,
        childElement.color,
        childElement.textColor,
        childElement.textSize,
        childElement.textFont,
        childElement.textWeight,
        childElement.textAlign,
        childElement.workspaceParameters[workspaceId].width,
        childElement.workspaceParameters[workspaceId].height,
        childElement.workspaceParameters[workspaceId].anchors,
        store.state.displayShadows,
      );

      element.graphicElement.off();
      element.graphicElement.each(function () {
        this.off();
      }, true);
      boxGroup.add(element.graphicElement);
    }

    // We calculate the difference of coordinates between the box and the boxgroup to offset the position
    const xDiff = boxGroup.cx() - box.cx();
    const yDiff = boxGroup.cy() - box.cy();
    boxGroup.center(position.x + xDiff, position.y + yDiff);

    // Update anchor positions after moving the box
    boxGroup.topAnchor.center(position.x, position.y - height / 2);
    boxGroup.bottomAnchor.center(position.x, position.y + height / 2);
    boxGroup.leftAnchor.center(position.x - width / 2, position.y);
    boxGroup.rightAnchor.center(position.x + width / 2, position.y);

    // Note: Anchors are now updated in state by core.js, not here

    // Get the original element's connections
    const originalElement = store.state.allSimpleElements.get(elementId);
    if (originalElement) {
      // Store the original element's destination element IDs for connection handling
      expandedWorkspaceBox.destinationElementIds =
        originalElement.workspaceParameters[store.state.currentWorkspaceId]
          ?.destinationElementIds || [];
    }

    setupExpandedBoxListener(expandedWorkspaceBox);
    // Store the box group in the parameters
    drawAdapter.parameters.expandedWorkspaceBoxes.set(workspaceId, boxGroup);

    // Store the expanded workspace box object for reference
    boxGroup.expandedWorkspaceBox = expandedWorkspaceBox;

    // Store initial dimensions for resizing calculations
    expandedWorkspaceBox.initialWidth = width;
    expandedWorkspaceBox.initialHeight = height;
    expandedWorkspaceBox.initialTopLeftX = position.x - width / 2;
    expandedWorkspaceBox.initialTopLeftY = position.y - height / 2;

    return boxGroup;
  },

  /**
   * Removes the expanded workspace box for an element
   * @param {string} workspaceId - The ID of the workspace
   */
  removeExpandedWorkspaceBox: (workspaceId) => {
    const boxGroup =
      drawAdapter.parameters.expandedWorkspaceBoxes.get(workspaceId);
    if (boxGroup) {
      // Clean up all components
      boxGroup.collapseButtonGroup?.remove();
      boxGroup.collapseButton = null;
      boxGroup.collapseButtonBg = null;
      boxGroup.collapseButtonGroup = null;

      // Remove the entire box group
      boxGroup.remove();
      drawAdapter.parameters.expandedWorkspaceBoxes.delete(workspaceId);
    }
  },

  /**
   * get center coordinates of an expanded workspace
   * @param workspaceId
   */
  getExpandedWorkspaceCenterCoordinates: (workspaceId) => {
    const boxGroup =
      drawAdapter.parameters.expandedWorkspaceBoxes.get(workspaceId);
    if (boxGroup) {
      return {
        x: boxGroup.cx(),
        y: boxGroup.cy(),
      };
    }
    return null;
  },

  /**
   * Moves an expanded workspace box to a specific position
   * @param {string} workspaceId - The ID of the workspace
   * @param {number} x - The new x coordinate
   * @param {number} y - The new y coordinate
   * @returns {Object} The box group that was moved
   */
  moveExpandedWorkspaceBox: (workspaceId, x, y) => {
    const boxGroup =
      drawAdapter.parameters.expandedWorkspaceBoxes.get(workspaceId);
    if (!boxGroup) {
      console.error(`Box group for workspace ID ${workspaceId} not found`);
      return null;
    }

    const offset = {
      x: boxGroup.cx() - boxGroup.box.cx(),
      y: boxGroup.cy() - boxGroup.box.cy(),
    };

    // Calculate the movement delta
    const currentPosition = {
      x: boxGroup.cx(),
      y: boxGroup.cy(),
    };
    const dx = x - currentPosition.x;
    const dy = y - currentPosition.y;

    // Move the box group
    boxGroup.dmove(dx + offset.x, dy + offset.y);

    return boxGroup;
  },

  /**
   * Resizes an expanded workspace box and its child elements
   * @param {string} workspaceId - The ID of the workspace
   * @param {number} newWidth - The new width of the box
   * @param {number} newHeight - The new height of the box
   * @returns {Object} The box group that was resized with scale factors
   */
  resizeExpandedWorkspaceBox: (workspaceId, newWidth, newHeight) => {
    const boxGroup =
      drawAdapter.parameters.expandedWorkspaceBoxes.get(workspaceId);
    if (!boxGroup) {
      console.error(`Box group for workspace ID ${workspaceId} not found`);
      return null;
    }

    const box = boxGroup.box;
    const oldWidth = box.width();
    const oldHeight = box.height();

    // Preserve top-left position (like regular elements)
    const topLeftX = box.x();
    const topLeftY = box.y();

    // Calculate scale factors based on initial dimensions if available
    const expandedWorkspaceBox = boxGroup.expandedWorkspaceBox;
    const initialWidth = expandedWorkspaceBox.initialWidth || oldWidth;
    const initialHeight = expandedWorkspaceBox.initialHeight || oldHeight;

    // Store initial dimensions if not already stored
    if (!expandedWorkspaceBox.initialWidth) {
      expandedWorkspaceBox.initialWidth = oldWidth;
      expandedWorkspaceBox.initialHeight = oldHeight;
      expandedWorkspaceBox.initialTopLeftX = topLeftX;
      expandedWorkspaceBox.initialTopLeftY = topLeftY;
    }

    // Store initial child element positions if not already stored (for drag operations)
    if (!expandedWorkspaceBox.initialChildPositions) {
      expandedWorkspaceBox.initialChildPositions = new Map();
    }

    const scaleX = newWidth / initialWidth;
    const scaleY = newHeight / initialHeight;

    // Resize the box while preserving top-left position
    box.width(newWidth).height(newHeight).move(topLeftX, topLeftY);

    // Calculate new center after resize
    const newCenterX = topLeftX + newWidth / 2;
    const newCenterY = topLeftY + newHeight / 2;

    // Update resizer position
    boxGroup.resizer.move(topLeftX + newWidth - 15, topLeftY + newHeight - 15);

    // Update collapse button position
    if (boxGroup.collapseButtonBg) {
      boxGroup.collapseButtonBg.center(topLeftX + newWidth - 18, topLeftY - 10);
    }
    if (boxGroup.collapseButton) {
      boxGroup.collapseButton.center(
        boxGroup.collapseButtonBg.cx(),
        boxGroup.collapseButtonBg.cy(),
      );
    }

    // Update anchors based on new center and bring them to front
    boxGroup.topAnchor.center(newCenterX, newCenterY - newHeight / 2);
    boxGroup.topAnchor.front();
    boxGroup.bottomAnchor.center(newCenterX, newCenterY + newHeight / 2);
    boxGroup.bottomAnchor.front();
    boxGroup.leftAnchor.center(newCenterX - newWidth / 2, newCenterY);
    boxGroup.leftAnchor.front();
    boxGroup.rightAnchor.center(newCenterX + newWidth / 2, newCenterY);
    boxGroup.rightAnchor.front();

    // Note: Anchors are now updated in state by core.js, not here

    return {
      boxGroup,
      scaleX,
      scaleY,
      centerX: newCenterX,
      centerY: newCenterY,
      initialWidth,
      initialHeight,
      initialCenterX: expandedWorkspaceBox.initialTopLeftX + initialWidth / 2,
      initialCenterY: expandedWorkspaceBox.initialTopLeftY + initialHeight / 2,
    };
  },

  /**
   * Captures the initial positions of child elements when resize drag starts
   * @param {string} workspaceId - The ID of the workspace
   * @param {Array} childElementIds - Array of child element IDs
   */
  captureInitialChildPositions: (workspaceId, childElementIds) => {
    const boxGroup =
      drawAdapter.parameters.expandedWorkspaceBoxes.get(workspaceId);
    if (!boxGroup) {
      return;
    }

    const expandedWorkspaceBox = boxGroup.expandedWorkspaceBox;
    expandedWorkspaceBox.initialChildPositions = new Map();

    for (const childElementId of childElementIds) {
      const graphicElement =
        drawAdapter.parameters.allElements.get(childElementId);
      if (graphicElement) {
        const entity = graphicElement.graphicElement.entity;
        expandedWorkspaceBox.initialChildPositions.set(childElementId, {
          x: entity.cx(),
          y: entity.cy(),
          width: entity.width(),
          height: entity.height(),
        });
      }
    }
  },

  /**
   * Clears the initial child positions (called on drag end)
   * @param {string} workspaceId - The ID of the workspace
   */
  clearInitialChildPositions: (workspaceId) => {
    const boxGroup =
      drawAdapter.parameters.expandedWorkspaceBoxes.get(workspaceId);
    if (boxGroup && boxGroup.expandedWorkspaceBox) {
      boxGroup.expandedWorkspaceBox.initialChildPositions = null;
    }
  },

  /**
   * Updates the reference dimensions and position after a successful resize
   * @param {string} workspaceId - The ID of the workspace
   */
  updateExpandedWorkspaceReferences: (workspaceId) => {
    const boxGroup =
      drawAdapter.parameters.expandedWorkspaceBoxes.get(workspaceId);
    if (!boxGroup) {
      return;
    }

    const box = boxGroup.box;
    const expandedWorkspaceBox = boxGroup.expandedWorkspaceBox;

    // Update the reference dimensions and position to current values
    expandedWorkspaceBox.initialWidth = box.width();
    expandedWorkspaceBox.initialHeight = box.height();
    expandedWorkspaceBox.initialTopLeftX = box.x();
    expandedWorkspaceBox.initialTopLeftY = box.y();
  },

  hideConnectedAnchors: (elementId) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    const elementEntity = element.graphicElement.entity;
    if (elementEntity.topAnchor) {
      elementEntity.topAnchor.remove();
    }
    if (elementEntity.bottomAnchor) {
      elementEntity.bottomAnchor.remove();
    }
    if (elementEntity.leftAnchor) {
      elementEntity.leftAnchor.remove();
    }
    if (elementEntity.rightAnchor) {
      elementEntity.rightAnchor.remove();
    }
  },
  startDragging: (elementId, x, y) => {
    const element = drawAdapter.parameters.allElements.get(elementId);
    if (element.graphicElement.graphicTempLink) {
      element.graphicElement.graphicTempLink.remove();
    }
    element.graphicElement.graphicTempLink = draw
      .line(
        element.graphicElement.entity.cx(),
        element.graphicElement.entity.cy(),
        x * (draw.viewbox().width / 10000) + draw.viewbox().x,
        y * (draw.viewbox().width / 10000) + draw.viewbox().y,
      )
      .stroke({
        color: "#a5a5a5",
        width: 2,
        dasharray: 3,
      });
    element.graphicElement.graphicTempLink.back();
    element.graphicElement.graphicTempLink.forward();
  },
  endDragging: (sourceElementId) => {
    const sourceElement =
      drawAdapter.parameters.allElements.get(sourceElementId);
    if (sourceElement.graphicElement.graphicTempLink) {
      sourceElement.graphicElement.graphicTempLink.remove();
    }
  },
};

function createBigPattern(colorBigSquares, smallPattern) {
  return (add) => {
    add.fill(smallPattern);
    add.rect(100, 100).stroke({
      color: colorBigSquares || "#d5d2d2",
      width: 1,
    });
  };
}

function createSmallPattern(color, colorSmallSquares) {
  return (add2) => {
    add2
      .rect(10, 10)
      .fill(color || "#fff")
      .stroke({
        color: colorSmallSquares || "#f3ebeb",
        width: 1,
      });
  };
}

const configureAnchor = (element) => {
  // Check if the element is a regular element or an expanded workspace box
  const isExpandedWorkspace = !element.graphicElement;

  // Create the anchor circle
  let anchor;
  if (isExpandedWorkspace) {
    // For expanded workspace boxes
    anchor = element.graphicWorkspaceBox.circle(5);
    anchor
      .fill("#000")
      .stroke({
        color: "#000",
        width: 3,
      })
      .attr({ cursor: "pointer" })
      .draggable();
    // We don't set up listeners here as they'll be set up separately
  } else {
    // For regular elements
    anchor = element.graphicElement.circle(5);
    anchor
      .fill("#000")
      .stroke({
        color: "#000",
        width: 3,
      })
      .attr({ cursor: "pointer" })
      .draggable();
    setupAnchorsListeners(anchor, element);
  }

  return anchor;
};

// Function to determine if the source anchor is on a vertical side (top/bottom)
// or a horizontal side (left/right) of the element
function isSourceAnchorVertical(connectedAnchors) {
  // Get the source and destination anchors
  const source = connectedAnchors.anchorFrom;
  const dest = connectedAnchors.anchorTo;

  // Calculate the differences in x and y coordinates
  const dx = Math.abs(dest.x - source.x);
  const dy = Math.abs(dest.y - source.y);

  // If the source is closer to the destination horizontally than vertically,
  // it's likely a left or right anchor
  // If the source is closer to the destination vertically than horizontally,
  // it's likely a top or bottom anchor

  // Special case handling for elements that are diagonally positioned
  // We want to ensure the path is always perpendicular to the source element

  // Case 1: Source is above destination
  if (source.y < dest.y && dx <= dy * 1.5) {
    // If source is above destination and dx is not much larger than dy,
    // treat it as a vertical anchor (bottom)
    return true;
  }

  // Case 2: Source is below destination
  if (source.y > dest.y && dx <= dy * 1.5) {
    // If source is below destination and dx is not much larger than dy,
    // treat it as a vertical anchor (top)
    return true;
  }

  // Case 3: Source is to the left of destination
  if (source.x < dest.x && dy <= dx * 1.5) {
    // If source is to the left of destination and dy is not much larger than dx,
    // treat it as a horizontal anchor (right)
    return false;
  }

  // Case 4: Source is to the right of destination
  if (source.x > dest.x && dy <= dx * 1.5) {
    // If source is to the right of destination and dy is not much larger than dx,
    // treat it as a horizontal anchor (left)
    return false;
  }

  // General case: if dy is greater than dx, it's likely a vertical anchor
  return dy >= dx;
}
