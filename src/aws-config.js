const PUBLIC_API_KEY = "ukfRSACY6Q8hjGGy1szIN9qi55jzceoY4JT1OmMN";

export async function generateShareLink(
  workspaceName,
  displayGrid,
  displayShadows,
) {
  const response = await fetch(
    "https://izhahapzvg.execute-api.ap-southeast-2.amazonaws.com/prod/saveToS3AndGeneratePreSignedURL",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": PUBLIC_API_KEY,
      },
      body: JSON.stringify({ operation: "download", key: workspaceName }),
    },
  );
  const jsonResponse = await response.json();
  const presignedUrl = jsonResponse.presignedUrl;
  const encodedUrl = encodeURIComponent(presignedUrl);

  return `https://player.simulaction.io?autoplay=true&displayGrid=${displayGrid}&displayShadows=${displayShadows}&workspaceUrl=${encodedUrl}`;
}

export async function saveWorkspaceToS3(workspaceData, workspaceName) {
  const response = await fetch(
    "https://izhahapzvg.execute-api.ap-southeast-2.amazonaws.com/prod/saveToS3AndGeneratePreSignedURL",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": PUBLIC_API_KEY,
      },
      body: JSON.stringify({
        operation: "upload",
        key: workspaceName,
        workspaceData,
      }),
    },
  );
  if (!response.ok) {
    throw new Error("Failed to upload workspace");
  }
}

export async function sendFeedback(feedback) {
  const response = await fetch(
    "https://jgy9lv14wg.execute-api.ap-southeast-2.amazonaws.com/prod/sendFeedback",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": PUBLIC_API_KEY,
      },
      body: JSON.stringify(feedback),
    },
  );
  if (!response.ok) {
    throw new Error("Failed to send feedback");
  }
}
