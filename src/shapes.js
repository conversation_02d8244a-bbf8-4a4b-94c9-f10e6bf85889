export function getGraphicShape(element, type) {
  switch (type) {
    case "TEXT-CLASSIC":
      return element.graphicElement.rect(200, 50);
    case "TEXT-STYLE":
      return element.graphicElement.text("Add your text");
    case "SQUARE":
      return element.graphicElement.rect(100, 100);
    case "CIRCLE":
      return element.graphicElement.ellipse(100, 100);
    case "TRIANGLE":
      return element.graphicElement.path(
        "m0.25383,99.836l49.81242,-97.37484l49.81242,97.37484l-99.62484,0z",
      );
    case "STAR_5":
      return element.graphicElement.path(
        "m1.12258,39.53956l37.48062,0l11.5818,-36.76427l11.58181,36.76427l37.48061,0l-30.3224,22.72131l11.5824,36.76427l-30.32242,-22.72193l-30.32241,22.72193l11.5824,-36.76427l-30.32241,-22.72131z",
      );
    case "RHOMBUS":
      return element.graphicElement.path(
        "m0.00508,50.86921l49.81242,-48.74992l49.81242,48.74992l-49.81242,48.74992l-49.81242,-48.74992z",
      );
    case "PENTAGON":
      return element.graphicElement.path(
        "m0.50258,39.47531l49.68736,-37.19378l49.68748,37.19378l-18.97884,60.18106l-61.4171,0l-18.9789,-60.18106z",
      );
    case "HEXAGON":
      return element.graphicElement.path(
        "m0.12945,51.03751l21.37497,-48.68742l56.99991,0l21.37496,48.68742l-21.37496,48.68742l-56.99991,0l-21.37497,-48.68742z",
      );
    case "OCTAGON":
      return element.graphicElement.path(
        "m0.25383,30.76395l29.14268,-28.52028l41.21448,0l29.14268,28.52028l0,40.33428l-29.14268,28.52028l-41.21448,0l-29.14268,-28.52028l0,-40.33428z",
      );
    case "CYLINDER":
      return element.graphicElement.path(
        "m99.75555,18.59677c0,8.96311 -22.35775,16.22914 -49.93743,16.22914m49.93743,-16.22914l0,0c0,8.96311 -22.35775,16.22914 -49.93743,16.22914c-27.57967,0 -49.93741,-7.26602 -49.93741,-16.22914m0,0l0,0c0,-8.9631 22.35774,-16.22914 49.93741,-16.22914c27.57968,0 49.93743,7.26604 49.93743,16.22914l0,64.91659c0,8.9631 -22.35775,16.22911 -49.93743,16.22911c-27.57967,0 -49.93741,-7.26602 -49.93741,-16.22911l0,-64.91659z",
      );
    case "DATABASE":
      return element.graphicElement.path(
        "M3 4v16c0 2.855 7.5 3 9 3s9-.145 9-3V4c0-2.855-7.5-3-9-3s-9 .145-9 3zm9 3a22.579 22.579 0 0 0 7-.953v5.782C17.878 12.776 13.483 13 12 13c-1.676 0-5.975-.3-7-1.171V6.047A22.227 22.227 0 0 0 12 7zm7 12.832c-.518.43-2.971 1.168-7 1.168s-6.482-.738-7-1.168v-5.785A22.2 22.2 0 0 0 12 15a22.548 22.548 0 0 0 7-.953zM18.726 4C17.2 4.76 13.876 5 12 5c-1.757 0-5.163-.22-6.726-1A17.5 17.5 0 0 1 12 3a17.5 17.5 0 0 1 6.726 1z",
      );
    case "WORLD_WIRE":
      return element.graphicElement.path(
        "m10.50885,74.53365l78.18532,0.02985l-2.53713,3.74949l-73.0811,-0.08954l-2.56709,-3.6898l0,0zm0.5373,-49.87675l77.14057,-0.08954l2.23871,3.84641l-81.64786,0.02985l2.26859,-3.78671zm-7.61283,24.99257l92.30649,-0.08954l-0.11932,3.84641l-92.12748,-0.05969l-0.0597,-3.69717zm92.3829,0.35675c0,25.46413 -20.72074,46.17946 -46.19416,46.17946c-25.46241,0 -46.17929,-20.71907 -46.17929,-46.17946c0,-25.46051 20.72062,-46.17584 46.17929,-46.17584c25.47342,-0.00374 46.19416,20.71533 46.19416,46.17584zm-46.19053,-49.87675c-27.49497,0 -49.86926,22.37262 -49.86926,49.873c0,27.50413 22.37429,49.87674 49.86926,49.87674c27.50994,0 49.88049,-22.37261 49.88049,-49.87674c0,-27.50039 -22.37055,-49.873 -49.88049,-49.873zm13.99471,49.87675c0,27.21054 -7.37267,46.17946 -13.99471,46.17946c-6.61455,0 -13.9871,-18.96892 -13.9871,-46.17946c0,-27.21804 7.36892,-46.17584 13.9871,-46.17584c6.6183,-0.00374 13.99471,18.9578 13.99471,46.17584zm-13.99471,-49.87675c-11.48619,0 -17.68455,25.69819 -17.68455,49.873c0,24.18219 6.19836,49.87674 17.68455,49.87674c11.48631,0 17.6959,-25.69829 17.6959,-49.87674c-0.00374,-24.17481 -6.2096,-49.873 -17.6959,-49.873zm32.77555,49.87675c0,25.46413 -14.70814,46.17946 -32.77555,46.17946c-18.06366,0 -32.76431,-20.71907 -32.76431,-46.17946c0,-25.46051 14.70065,-46.17584 32.76431,-46.17584c18.06741,-0.00374 32.77555,20.71533 32.77555,46.17584zm-32.77555,-49.87675c-20.10007,0 -36.46176,22.37262 -36.46176,49.873c0,27.50413 16.36169,49.87674 36.46176,49.87674c20.10756,0 36.46562,-22.37261 36.46562,-49.87674c0,-27.50039 -16.35806,-49.873 -36.46562,-49.873z",
      );
    case "ENVELOPE":
      return element.graphicElement.path(
        "m96.1349,20.66029l-92.25469,0l46.12735,24.3424l46.12735,-24.3424zm-43.39663,34.71224c-0.84759,0.44863 -1.78731,0.67466 -2.72703,0.67466s-1.87944,-0.22603 -2.73072,-0.67466l-47.01916,-24.80473l0,50.59234l99.49975,0l0,-50.59234l-47.02284,24.80473z",
      );
    case "RSS":
      return element.graphicElement.path(
        "m-0.21183,53.26299c12.42385,0 24.10501,4.84453 32.88,13.63302c8.79518,8.79251 13.63071,20.50783 13.63071,32.97495l19.15242,0c0,-36.21535 -29.46123,-65.67249 -65.66717,-65.67249l0,19.06452l0.00404,0zm0.02422,-33.80314c44.29073,0 80.31504,36.08268 80.31504,80.43121l19.14435,0c0,-54.86176 -44.61767,-99.49975 -99.46342,-99.49975l0,19.06854l0.00404,0zm26.47837,67.10373c0,7.29694 -5.93745,13.2149 -13.25937,13.2149s-13.25937,-5.91796 -13.25937,-13.2149c0,-7.2889 5.93745,-13.20686 13.25937,-13.20686s13.25937,5.91394 13.25937,13.20686z",
      );
    case "CLOUD":
      return element.graphicElement.path(
        "m21.48027,75.40713c-11.85631,0 -21.47234,-9.27908 -21.47234,-20.71995l0,0c0.00716,-9.18239 6.20551,-16.91784 14.76939,-19.6287l0,0c0.29703,-7.80451 6.93199,-14.03431 15.0879,-14.04122l0,0c2.61247,0 5.06389,0.65613 7.19323,1.78537l0,0c4.53066,-7.26925 12.77604,-12.14189 22.21671,-12.14534l0,0c14.12522,0.00691 25.61292,10.89524 25.93858,24.44609l0,0c8.45294,2.77647 14.53677,10.47739 14.54393,19.58381l0,0c0,11.44087 -9.61603,20.71995 -21.47234,20.71995l0,0l-56.80507,0l0,0zm59.41038,-34.27425c-1.82873,-0.32807 -3.09202,-1.94077 -2.91666,-3.72614l0,0c0.06084,-0.59052 0.09663,-1.16032 0.09663,-1.7163l0,0c-0.02505,-10.01119 -8.41716,-18.11269 -18.79187,-18.1265l0,0c-8.04855,-0.00691 -14.89465,4.89681 -17.57511,11.77584l0,0c-0.4366,1.11542 -1.45296,1.94768 -2.67688,2.17559l0,0c-1.21677,0.22792 -2.48363,-0.17957 -3.31748,-1.06708l0,0c-1.48875,-1.56436 -3.52862,-2.52438 -5.84405,-2.52438l0,0c-4.38394,0.00691 -7.93045,3.42915 -7.94477,7.65947l0,0c0,0.46965 0.06084,0.95312 0.16104,1.46421l0,0c0.17536,0.91858 -0.02863,1.8648 -0.58333,2.63143l0,0c-0.5547,0.77009 -1.40286,1.28118 -2.35122,1.42968l0,0c-6.79957,1.08089 -11.99588,6.73053 -11.98872,13.57502l0,0c0.01431,7.62839 6.41307,13.79603 14.31847,13.81675l0,0l56.80865,0c7.9054,-0.02072 14.29342,-6.18836 14.31131,-13.81675l0,0c0.01431,-6.75816 -5.046,-12.3629 -11.706,-13.55085l0,0",
      );
    case "SERVER":
      return element.graphicElement.path(
        "M80 30H20C18.9 30 18 30.9 18 32V68C18 69.1 18.9 70 20 70H80C81.1 70 82 69.1 82 68V32C82 30.9 81.1 30 80 30ZM20 26C16.7 26 14 28.7 14 32V68C14 71.3 16.7 74 20 74H80C83.3 74 86 71.3 86 68V32C86 28.7 83.3 26 80 26H20ZM30 58H70V56H30V58ZM30 52H70V50H30V52ZM30 46H70V44H30V46Z",
      );
    default:
      return element.graphicElement.image(type);
  }
}
