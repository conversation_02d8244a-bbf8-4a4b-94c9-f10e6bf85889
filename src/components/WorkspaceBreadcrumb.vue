<template>
  <div class="workspace-breadcrumb-container">
    <v-breadcrumbs
      :items="breadcrumbItems"
      class="workspace-breadcrumb"
      color="white"
      active-color="white"
      divider=">"
    >
      <template v-slot:divider>
        <v-icon color="white" size="small">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
          :to="item.to"
          :disabled="item.disabled"
          @click="navigateToWorkspace(item.workspaceId)"
        >
          {{ item.title }}
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import { enterWorkspaceWithZoomOut } from "@/core";

export default {
  name: "WorkspaceBreadcrumb",
  computed: {
    ...mapState({
      currentWorkspaceId: (state) => state.currentWorkspaceId,
      rootWorkspaceId: (state) => state.rootWorkspaceId,
      allSimpleElements: (state) => state.allSimpleElements,
    }),
    ...mapGetters(["workspaces"]),
    breadcrumbItems() {
      const items = [];
      let currentWorkspace = this.workspaces.find(
        (w) => w.id === this.currentWorkspaceId,
      );

      // Add current workspace
      if (currentWorkspace) {
        items.push({
          title: currentWorkspace.name,
          disabled: currentWorkspace.id === this.currentWorkspaceId,
          workspaceId: currentWorkspace.id,
        });
      }

      // Work backwards through parent workspaces
      let parentElement = Array.from(this.allSimpleElements.values()).find(
        (element) => element.childWorkspaceId === currentWorkspace?.id,
      );

      while (parentElement) {
        // Get the first parent workspace ID (we'll use the first one for breadcrumb navigation)
        const parentWorkspaceId = parentElement?.parentWorkspaceIds[0];
        if (!parentWorkspaceId) break;

        const parentWorkspace = this.workspaces.find(
          (w) => w.id === parentWorkspaceId,
        );
        if (parentWorkspace) {
          items.unshift({
            title: parentWorkspace.name,
            disabled: false,
            workspaceId: parentWorkspace.id,
          });
        }
        parentElement = Array.from(this.allSimpleElements.values()).find(
          (element) => element.childWorkspaceId === parentWorkspaceId,
        );
      }

      return items;
    },
  },
  methods: {
    async navigateToWorkspace(workspaceId) {
      if (workspaceId !== this.currentWorkspaceId) {
        await enterWorkspaceWithZoomOut(workspaceId);
      }
    },
  },
};
</script>

<style scoped>
.workspace-breadcrumb-container {
  display: flex;
  align-items: center;
  height: 30px;
  width: 100%;
}

.workspace-breadcrumb {
  width: 100%;
}

:deep(.v-breadcrumbs-item) {
  color: white !important;
  opacity: 0.7;
  transition: opacity 0.2s;
  font-size: 0.875rem;
}

:deep(.v-breadcrumbs-item:hover) {
  opacity: 1;
}

:deep(.v-breadcrumbs-item--disabled) {
  opacity: 1;
  cursor: default;
}

:deep(.v-breadcrumbs-divider) {
  color: white !important;
  opacity: 0.7;
}
</style>
