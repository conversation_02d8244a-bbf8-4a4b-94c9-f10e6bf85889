<template>
  <v-dialog v-model="dialogVisible" width="600" persistent>
    <v-card color="#1c1f22">
      <v-card-title style="text-align: center">
        <span class="text-h5">Move Elements with Connections</span>
      </v-card-title>
      <v-card-text>
        <p>
          The selected elements are connected to {{ connectedCount }} other element(s) that are not selected.
        </p>
        <p>How would you like to proceed?</p>
      </v-card-text>
      <v-card-actions>
        <v-container>
          <v-row>
            <v-col cols="12">
              <v-btn
                @click="moveAll"
                color="#6d949b"
                variant="flat"
                block
                class="mb-2 py-2"
                height="auto"
                style="white-space: normal; line-height: 1.2;"
              >
                <v-icon class="mr-2">mdi-connection</v-icon>
                <span>Move all connected elements</span>
              </v-btn>
            </v-col>
            <v-col cols="12">
              <v-btn
                @click="moveSelected"
                color="#555555"
                variant="flat"
                block
                class="mb-2 py-2"
                height="auto"
                style="white-space: normal; line-height: 1.2;"
              >
                <v-icon class="mr-2">mdi-link-off</v-icon>
                <span>Move only selected elements (remove connections)</span>
              </v-btn>
            </v-col>
            <v-col cols="12">
              <v-btn
                @click="cancel"
                color="#333333"
                variant="flat"
                block
                class="py-2"
                height="auto"
                style="white-space: normal; line-height: 1.2;"
              >
                <v-icon class="mr-2">mdi-cancel</v-icon>
                <span>Cancel (don't move anything)</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-container>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: "MoveElementDialog",
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    connectedCount: {
      type: Number,
      required: true
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      }
    }
  },
  methods: {
    moveAll() {
      this.$emit("decision", "moveAll");
      this.dialogVisible = false;
    },
    moveSelected() {
      this.$emit("decision", "moveSelected");
      this.dialogVisible = false;
    },
    cancel() {
      this.$emit("decision", "cancel");
      this.dialogVisible = false;
    }
  }
};
</script>
