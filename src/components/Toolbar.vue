<template>
  <v-navigation-drawer clipped app color="#2a2d33" width="70" permanent>
    <v-menu
      v-model="selectorMenu"
      :close-on-content-click="true"
      location="right"
    >
      <template v-slot:activator="{ props }">
        <v-tooltip open-on-hover open-delay="600">
          <template v-slot:activator="{ props: propsTooltip }">
            <v-btn
              height="50"
              color="#2a2d33"
              block
              v-bind="{ ...props, ...propsTooltip }"
              id="selectionButton"
              size="x-large"
              v-show="diagramMode !== 'PLAYBACK_MODE'"
            >
              <v-icon color="white">
                {{
                  selectionMode === "GRABBER"
                    ? "mdi-hand-back-right-outline"
                    : "mdi-arrow-top-left"
                }}
              </v-icon>
            </v-btn>
          </template>
          Selection Mode
        </v-tooltip>
      </template>
      <v-card density="comfortable" color="#2a2d33">
        <v-container>
          <v-row justify="center" align="center">
            <v-col cols="auto">
              <v-btn
                icon="mdi-hand-back-right-outline"
                @click="toggleSelectMode()"
              />
            </v-col>
            <v-col cols="auto">
              <v-btn
                icon="mdi-arrow-top-left"
                @click="toggleRectangularSelectMode()"
              />
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-menu>
    <v-menu
      v-model="shapeMenu"
      :close-on-content-click="false"
      location="right"
    >
      <template v-slot:activator="{ props }">
        <v-btn
          color="#2a2d33"
          block
          v-bind="props"
          id="shapeButton"
          size="x-large"
          v-show="diagramMode === 'CREATION_MODE'"
        >
          <v-icon color="white">
            {{ getShapeIcon }}
          </v-icon>
        </v-btn>
      </template>
      <v-card density="comfortable" color="#2a2d33">
        <v-tabs v-model="tabs" bg-color="#2a2d33" fixed-tabs>
          <v-tab value="text" style="color: #cef8ff">Text</v-tab>
          <v-tab value="classic" style="color: #cef8ff">Classic</v-tab>
          <v-tab value="web" style="color: #cef8ff">Web</v-tab>
          <v-tab value="image" style="color: #cef8ff">Web Image</v-tab>
          <v-tab value="localImage" style="color: #cef8ff">Local Image</v-tab>
        </v-tabs>
        <v-window v-model="tabs" style="background-color: #2a2d33">
          <v-window-item value="text">
            <v-container>
              <v-row justify="center" align="center">
                <v-col cols="auto">
                  <v-btn
                    id="TEXT-CLASSIC"
                    icon="mdi-format-text"
                    @click="setDefaultShape('TEXT-CLASSIC')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
              </v-row>
            </v-container>
          </v-window-item>
          <v-window-item value="classic">
            <v-container>
              <v-row justify="center" align="center">
                <v-col cols="auto">
                  <v-btn
                    id="SQUARE"
                    icon="mdi-square-outline"
                    @click="setDefaultShape('SQUARE')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="CIRCLE"
                    icon="mdi-circle-outline"
                    @click="setDefaultShape('CIRCLE')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="TRIANGLE"
                    icon="mdi-triangle-outline"
                    @click="setDefaultShape('TRIANGLE')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
              </v-row>
              <v-row justify="center" align="center">
                <v-col cols="auto">
                  <v-btn
                    id="RHOMBUS"
                    icon="mdi-rhombus-outline"
                    @click="setDefaultShape('RHOMBUS')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="STAR_5"
                    icon="mdi-star"
                    @click="setDefaultShape('STAR_5')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="CYLINDER"
                    icon="mdi-cylinder"
                    @click="setDefaultShape('CYLINDER')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
              </v-row>
              <v-row justify="center" align="center">
                <v-col cols="auto">
                  <v-btn
                    id="PENTAGON"
                    icon="mdi-pentagon-outline"
                    @click="setDefaultShape('PENTAGON')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="HEXAGON"
                    icon="mdi-hexagon-outline"
                    @click="setDefaultShape('HEXAGON')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="OCTAGON"
                    icon="mdi-octagon-outline"
                    @click="setDefaultShape('OCTAGON')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
              </v-row>
            </v-container>
          </v-window-item>
          <v-window-item value="web">
            <v-container>
              <v-row justify="center" align="center">
                <v-col cols="auto">
                  <v-btn
                    id="DATABASE"
                    icon="mdi-database"
                    @click="setDefaultShape('DATABASE')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="ENVELOPE"
                    icon="mdi-email-outline"
                    @click="setDefaultShape('ENVELOPE')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="WORLD_WIRE"
                    icon="mdi-web"
                    @click="setDefaultShape('WORLD_WIRE')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
              </v-row>
              <v-row justify="center" align="center">
                <v-col cols="auto">
                  <v-btn
                    id="CLOUD"
                    icon="mdi-cloud-outline"
                    @click="setDefaultShape('CLOUD')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="RSS"
                    icon="mdi-rss"
                    @click="setDefaultShape('RSS')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
                <v-col cols="auto">
                  <v-btn
                    id="SERVER"
                    icon="mdi-server"
                    @click="setDefaultShape('SERVER')"
                    draggable="true"
                    :ondragend="endDrag"
                  />
                </v-col>
              </v-row>
            </v-container>
          </v-window-item>
          <v-window-item value="image">
            <v-card-text style="color: white"
              >Please paste the desired URL in the box below</v-card-text
            >
            <v-text-field
              :value="imageUrl"
              v-model="imageUrl"
              clearable
              style="color: white"
              placeholder="http://example.com/image.png"
            />
            <v-btn
              color="#2a2d33"
              style="color: white"
              @click="setDefaultShape(imageUrl)"
            >
              OK
            </v-btn>
          </v-window-item>
          <v-window-item value="localImage">
            <v-card-text style="color: white">
              Select an image from your local files
            </v-card-text>
            <v-file-input
              label="Click here to select image"
              @change="handleImageUpload"
              prepend-icon="mdi-image"
            />
            <v-btn
              color="#2a2d33"
              style="color: white"
              @click="confirmLocalImage"
              :disabled="
                errorMessageLocalImage !== '' || localImageUrl === null
              "
            >
              OK
            </v-btn>
            <v-alert v-if="errorMessageLocalImage" type="error" dismissible>
              {{ errorMessageLocalImage }}
            </v-alert>
          </v-window-item>
        </v-window>
      </v-card>
    </v-menu>
    <v-btn
      color="#2a2d33"
      block
      @click="deleteSelected()"
      size="x-large"
      v-show="diagramMode === 'CREATION_MODE'"
    >
      <v-icon color="white"> mdi-delete </v-icon>
    </v-btn>
    <v-tooltip text="Undo" open-delay="600">
      <template v-slot:activator="{ props }">
        <v-btn
          color="#2a2d33"
          block
          @click="undo"
          size="x-large"
          v-show="diagramMode !== 'PLAYBACK_MODE'"
          v-bind="props"
        >
          <v-icon color="white"> mdi-undo </v-icon>
        </v-btn>
      </template>
    </v-tooltip>
    <v-tooltip text="Redo" open-delay="600">
      <template v-slot:activator="{ props }">
        <v-btn
          color="#2a2d33"
          block
          @click="redo"
          size="x-large"
          v-show="diagramMode !== 'PLAYBACK_MODE'"
          v-bind="props"
        >
          <v-icon color="white"> mdi-redo </v-icon>
        </v-btn>
      </template>
    </v-tooltip>
    <v-tooltip text="Continuous Mode" open-delay="600">
      <template v-slot:activator="{ props }">
        <v-btn
          color="#2a2d33"
          block
          @click="toggleContinuousMode"
          size="x-large"
          v-show="diagramMode !== 'CREATION_MODE' && sendDataIsSet"
          :disabled="disableFrameNavigation"
          v-bind="props"
          id="continuousModeButton"
        >
          <v-icon
            v-bind:class="{
              menuButtonOn: this.rifleModeState,
              menuButtonOff: !this.rifleModeState,
            }"
          >
            mdi-dots-horizontal-circle
          </v-icon>
        </v-btn>
      </template>
    </v-tooltip>
    <v-tooltip text="Director's View" open-delay="600">
      <template v-slot:activator="{ props }">
        <v-btn
          color="#2a2d33"
          block
          @click="toggleDirectorView"
          size="x-large"
          v-show="diagramMode !== 'CREATION_MODE'"
          v-bind="props"
          :disabled="disableDirectorsView"
          id="directorViewButton"
        >
          <v-icon
            v-bind:class="{
              menuButtonOn: this.immersiveViewState,
              menuButtonOff: !this.immersiveViewState,
            }"
          >
            mdi-movie-open-play-outline
          </v-icon>
        </v-btn>
      </template>
    </v-tooltip>
    <v-menu v-model="menu" :close-on-content-click="false" location="end">
      <template v-slot:activator="{ props }">
        <v-tooltip open-on-hover open-delay="600">
          <template v-slot:activator="{ props: tooltipProps }">
            <v-btn
              color="#2a2d33"
              block
              size="x-large"
              v-show="diagramMode !== 'CREATION_MODE'"
              v-bind="{ ...props, ...tooltipProps }"
            >
              <v-icon color="white">mdi-speedometer</v-icon>
            </v-btn>
          </template>
          Adjust animation speed
        </v-tooltip>
      </template>

      <v-card min-width="300" color="#1c1f22">
        <v-btn icon="mdi-minus" @click="decrementSpeed" color="#1c1f22"></v-btn>
        <v-slider
          v-model="getFramesPerSecond"
          min="0.05"
          max="5"
          step="0.05"
          density="compact"
          hide-details="true"
          style="display: inline-block; vertical-align: middle"
          width="100px"
        ></v-slider>
        <v-btn icon="mdi-plus" @click="incrementSpeed" color="#1c1f22"></v-btn>
        <span style="width: 7%" class="subheading mr-2">
          Speed: {{ getFramesPerSecond }} fps
        </span>
      </v-card>
    </v-menu>
    <v-tooltip text="Record Video" open-delay="600">
      <template v-slot:activator="{ props }">
        <v-btn
          color="#2a2d33"
          block
          @click="toggleRecordToVideo()"
          size="x-large"
          v-bind="props"
          id="recordVideoButton"
        >
          <v-icon
            v-bind:class="{
              recordingVideoSwitchedOn: this.recordingVideo,
              recordingVideoSwitchedOff: !this.recordingVideo,
            }"
          >
            mdi-video
          </v-icon>
        </v-btn>
      </template>
    </v-tooltip>
    <!-- Share Button -->
    <v-tooltip text="Share Link" open-delay="600">
      <template v-slot:activator="{ props }">
        <v-btn
          color="#2a2d33"
          block
          @click="openSharePopup"
          size="x-large"
          v-bind="props"
          id="shareButton"
        >
          <v-icon color="white"> mdi-share-variant </v-icon>
        </v-btn>
      </template>
    </v-tooltip>

    <!-- Share Link Popup -->
    <v-dialog v-model="showSharePopup" max-width="500">
      <v-card>
        <v-card-title class="headline">Generate Share Link</v-card-title>
        <v-card-text>
          <!-- Confirmation Step -->
          <div v-if="!isGenerating && !shareLink">
            Are you sure you want to save your workspace and generate a
            shareable link?
          </div>

          <!-- Spinner/Loading State -->
          <v-progress-circular
            v-if="isGenerating"
            indeterminate
            color="primary"
            class="d-flex justify-center my-5"
          ></v-progress-circular>

          <!-- Link Display -->
          <div v-if="shareLink" class="text-center">
            <p>Your shareable link:</p>
            <v-text-field
              v-model="shareLink"
              readonly
              append-icon="mdi-content-copy"
              @click:append="copyLink"
              outlined
            ></v-text-field>
            <v-alert type="success" v-if="linkCopied" border="left">
              Link copied to clipboard!
            </v-alert>
          </div>
        </v-card-text>
        <v-card-actions>
          <!-- Buttons for confirmation or closing -->
          <v-spacer></v-spacer>
          <v-btn
            v-if="!isGenerating && !shareLink"
            color="blue darken-1"
            text
            @click="generateShareLink"
          >
            Confirm
          </v-btn>
          <v-btn color="blue darken-1" text @click="closeSharePopup">
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-navigation-drawer>
</template>

<script>
import { mapActions, mapState } from "vuex";
import {
  createNewElementAtPosition,
  deleteElement,
  deleteSegment,
  getDeserializedWorkspaceWithMetadata,
  hideImmersiveViewbox,
  recordToVideo,
  redo,
  resetEntities,
  setDefaultShape,
  stopRecording,
  toggleRectangularSelectMode,
  toggleSelectMode,
  undo,
} from "@/core";
import { draw } from "@/plugins/svgjs";
import { generateShareLink, saveWorkspaceToS3 } from "@/aws-config";
import { setupObjectsAtFrame, smallZoomOut } from "@/animation";

const { v4: uuidv4 } = require("uuid");

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Toolbar",
  methods: {
    undo,
    redo,
    ...mapActions([
      "updateSpeedAction",
      "updateRecordingVideoAction",
      "showRightDrawerAction",
      "toggleRifleModeAction",
      "toggleImmersiveViewAction",
      "updateSequenceAction",
    ]),
    toggleRectangularSelectMode() {
      toggleRectangularSelectMode();
      resetEntities();
      hideImmersiveViewbox();
    },
    toggleSelectMode() {
      toggleSelectMode();
      resetEntities();
      hideImmersiveViewbox();
    },
    setDefaultShape(shape) {
      setDefaultShape(shape);
      this.shapeMenu = false;
    },
    toggleRecordToVideo() {
      if (!this.recordingVideo) {
        this.updateRecordingVideoAction(true);
        recordToVideo();
      } else {
        this.updateRecordingVideoAction(false);
        stopRecording();
      }
    },
    toggleContinuousMode() {
      this.toggleRifleModeAction(!this.rifleModeState);
    },
    toggleDirectorView() {
      let value = !this.immersiveViewState;
      this.toggleImmersiveViewAction(value);
      if (value === true) {
        setupObjectsAtFrame(
          this.currentSequenceId,
          this.currentSequence.currentFrame,
        );
      } else {
        smallZoomOut(this.currentSequenceId);
      }
    },
    endDrag(e) {
      const point = draw.point(e.pageX, e.pageY);
      createNewElementAtPosition(point.x, point.y, e.target.id);
    },
    async deleteSelected() {
      for (const elementId of this.selectedElementsIds) {
        await deleteElement({ elementId, undoable: true });
      }
      this.selectedSegmentsIds.forEach((segmentId) => {
        deleteSegment(segmentId, false, true);
      });
    },
    // Open the share popup
    openSharePopup() {
      this.showSharePopup = true;
      this.shareLink = ""; // Reset the share link
      this.linkCopied = false; // Reset copied state
    },
    // Close the share popup
    closeSharePopup() {
      this.showSharePopup = false;
      this.isGenerating = false;
      this.shareLink = "";
      this.linkCopied = false;
    },
    // Generate the share link
    async generateShareLink() {
      this.isGenerating = true;
      try {
        const workspaceData = getDeserializedWorkspaceWithMetadata();
        let workspaceName = this.diagramName + "-" + uuidv4();
        await saveWorkspaceToS3(workspaceData, workspaceName);
        this.shareLink = await generateShareLink(
          workspaceName,
          this.displayGrid,
          this.displayShadows,
        );

        // Stop loading state
        this.isGenerating = false;
      } catch (error) {
        console.error("Error generating share link:", error);
        this.isGenerating = false;
        this.shareLink = "Error generating link. Please try again.";
      }
    },
    // Copy the link to clipboard
    copyLink() {
      navigator.clipboard.writeText(this.shareLink).then(() => {
        this.linkCopied = true;
      });
    },
    adjustForScreenSize() {
      this.isSmallScreen = window.innerWidth < 768;
      this.drawerWidth = this.isSmallScreen ? 50 : 70;
    },
    decrementSpeed() {
      let updatedMillisecondsPerProcessingUnit =
        this.millisecondsPerProcessingUnit >= 1000 / 0.05
          ? 1000 / 0.05
          : Math.round(
              1000 / (1000 / this.millisecondsPerProcessingUnit - 0.05),
            );
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: updatedMillisecondsPerProcessingUnit,
      });
    },
    incrementSpeed() {
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: Math.round(
          1000 / (1000 / this.millisecondsPerProcessingUnit + 0.05),
        ),
      });
    },
    // Handle local file selection
    handleImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Validate file type
      if (file.type.startsWith("image/")) {
        this.errorMessageLocalImage = "";
      } else {
        this.errorMessageLocalImage = "Please select a valid image file.";
        return;
      }

      const maxSize = 0.5 * 1024 * 1024; // 500kB
      if (file.size > maxSize) {
        this.errorMessageLocalImage = "File size must be less than 5kB.";
        return;
      } else {
        this.errorMessageLocalImage = "";
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        this.localImageUrl = e.target.result; // Store base64 image URL
      };
      reader.readAsDataURL(file);
    },

    // Confirm local image selection
    confirmLocalImage() {
      if (this.localImageUrl) {
        this.setDefaultShape(this.localImageUrl);
      }
    },
  },
  mounted() {
    this.adjustForScreenSize();
    window.addEventListener("resize", this.adjustForScreenSize);
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.adjustForScreenSize);
  },
  computed: {
    ...mapState({
      allSimpleElements: (state) => state.allSimpleElements,
      allSimpleTexts: (state) => state.allSimpleTexts,
      sequences: (state) => state.sequences,
      currentSequence: (state) => state.sequences.get(state.currentSequenceId),
      currentSequenceId: (state) => state.currentSequenceId,
      selectedElementsIds: (state) => state.selectedElementsIds,
      selectedTextsIds: (state) => state.selectedTextsIds,
      selectedSegmentsIds: (state) => state.selectedSegmentsIds,
      millisecondsPerProcessingUnit: (state) =>
        state.millisecondsPerProcessingUnit,
      diagramName: (state) => state.diagramName,
      diagramMode: (state) => state.diagramMode,
      selectionMode: (state) => state.selectionMode,
      snapshots: (state) => state.snapshots,
      recordingVideo: (state) => state.recordingVideo,
      showRightDrawer: (state) => state.showRightDrawer,
      showEditionBar: (state) => state.showEditionBar,
      defaultShape: (state) => state.defaultShape,
      rifleModeState: (state) => state.rifleMode,
      immersiveViewState: (state) => state.immersiveView,
      displayGrid: (state) => state.displayGrid,
      displayShadows: (state) => state.displayShadows,
    }),
    getShapeIcon() {
      if (this.defaultShape === "SQUARE") {
        return "mdi-square-outline";
      } else if (this.defaultShape === "CIRCLE") {
        return "mdi-circle-outline";
      } else if (this.defaultShape === "TRIANGLE") {
        return "mdi-triangle-outline";
      } else if (this.defaultShape === "RHOMBUS") {
        return "mdi-rhombus-outline";
      } else if (this.defaultShape === "PENTAGON") {
        return "mdi-pentagon-outline";
      } else if (this.defaultShape === "HEXAGON") {
        return "mdi-hexagon-outline";
      } else if (this.defaultShape === "OCTAGON") {
        return "mdi-octagon-outline";
      } else if (this.defaultShape === "CYLINDER") {
        return "mdi-cylinder";
      } else if (this.defaultShape === "STAR_5") {
        return "mdi-star";
      } else if (this.defaultShape === "TEXT-CLASSIC") {
        return "mdi-format-text";
      } else if (this.defaultShape === "TEXT-STYLE") {
        return "mdi-format-text-variant-outline";
      } else if (this.defaultShape === "DATABASE") {
        return "mdi-database";
      } else if (this.defaultShape === "ENVELOPE") {
        return "mdi-email-outline";
      } else if (this.defaultShape === "RSS") {
        return "mdi-rss";
      } else if (this.defaultShape === "CLOUD") {
        return "mdi-cloud-outline";
      } else if (this.defaultShape === "SERVER") {
        return "mdi-server";
      } else if (this.defaultShape === "WORLD_WIRE") {
        return "mdi-web";
      } else return "mdi-image";
    },
    sendDataIsSet() {
      const currentSequence = this.sequences?.get(this.currentSequenceId);
      return currentSequence?.frames?.some((frame) =>
        frame.some((action) => action.metadata.type === "sendData"),
      );
    },
    disableFrameNavigation() {
      return (
        !this.currentSequence ||
        (this.currentSequence.isPlaying && !this.currentSequence.isPaused)
      );
    },
    disableDirectorsView() {
      return (
        !this.immersiveViewState &&
        (!this.currentSequence ||
          this.currentSequence.currentFrame === null ||
          !this.currentSequence.frames[
            Math.floor(this.currentSequence.currentFrame)
          ]?.some((action) => action.metadata.type === "viewboxPosition"))
      );
    },
    getFramesPerSecond: {
      get() {
        return (
          Math.round((1000 / this.millisecondsPerProcessingUnit) * 100) / 100
        );
      },
      set(value) {
        this.updateSpeedAction({ millisecondsPerProcessingUnit: 1000 / value });
      },
    },
  },
  data: function () {
    return {
      currentFrameSliderValue: 0,
      tabs: "classic",
      editingSequenceName: false,
      editingElementName: false,
      editingTextContent: false,
      currentSequenceDisplayOffFrame: null,
      currentSequenceDisplayOnFrame: null,
      selectorMenu: false,
      shapeMenu: false,
      imageUrl: null,
      showSharePopup: false, // Controls popup visibility
      isGenerating: false, // Indicates if the link is being generated
      shareLink: "", // Stores the generated share link
      linkCopied: false, // Tracks if the link was copied
      drawerVisible: true,
      isSmallScreen: false,
      drawerWidth: 70,
      menu: false,
      localImageUrl: null,
      errorMessageLocalImage: "",
    };
  },
};
</script>
<style scoped>
.recordingVideoSwitchedOn {
  color: red !important;
}

.recordingVideoSwitchedOff {
  color: white !important;
}

.menuButtonOn {
  color: #6cffd8 !important;
}

.menuButtonOff {
  color: white !important;
}

/* Center the popup content */
.text-center {
  text-align: center;
}

/* Adjust the spinner margin */
.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.v-navigation-drawer {
  transition: width 0.3s;
}

.menu-toggle {
  display: none;
}

@media (max-width: 768px) {
  .v-navigation-drawer {
    width: 50px !important;
  }
  .menu-toggle {
    display: block;
  }
}
</style>
