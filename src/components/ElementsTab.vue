<template>
  <v-card color="#2a2d32ff" id="elementsTab">
    <!-- Main Elements Tab Header -->
    <v-tabs v-model="elementsSubHeaderTab" bg-color="#2a2d32ff" fixed-tabs>
      <v-tab
        value="edit"
        style="color: #eddb99"
        v-show="selectedElementsIds.length > 0"
      >
        Edit
      </v-tab>
      <v-tab value="list" style="color: #eddb99"> List </v-tab>
    </v-tabs>
    <v-window
      v-model="elementsSubHeaderTab"
      style="background-color: #2a2d32ff"
    >
      <!-- EDIT TAB -->
      <v-window-item value="edit" v-show="selectedElementsIds.length > 0">
        <template
          v-if="
            selectedElementsIds.length > 0 && elementsSubHeaderTab === 'edit'
          "
        >
          <v-card color="#2a2d32ff">
            <!-- Element Name Editing & Shape Selection (unchanged) -->
            <!-- Element Name Editing -->
            <template v-if="editingElementName">
              <v-form @submit.prevent="submitElementName">
                <v-text-field
                  :value="tempElementName"
                  @update:model-value="tempElementName = $event"
                  clearable
                  style="color: white"
                />
                <v-btn color="#2a2d32ff" type="submit" style="color: white">
                  OK
                </v-btn>
              </v-form>
            </template>
            <template v-else>
              <v-btn color="#2a2d32ff" @click="editElementName">
                {{
                  allSimpleElements
                    .get(selectedElementsIds[0])
                    .name.substr(0, 20)
                    .concat(
                      allSimpleElements.get(selectedElementsIds[0]).name
                        .length > 20
                        ? "..."
                        : "",
                    )
                }}
                <v-icon style="color: white"> mdi-pencil </v-icon>
              </v-btn>
            </template>
            <div class="pa-2">
              <!-- Create/Enter Workspace Button -->
              <v-btn
                v-if="selectedElementsIds.length === 1 && diagramMode === 'CREATION_MODE'"
                color="#2a2d32ff"
                class="create-workspace-button"
                @click="handleWorkspaceAction"
                :disabled="false"
              >
                <v-icon>{{
                  hasWorkspace ? "mdi-folder-open" : "mdi-folder-plus"
                }}</v-icon>
                {{ hasWorkspace ? "Enter Workspace" : "Create Workspace" }}
              </v-btn>
              <!-- Shape Selection Menu -->
              <v-menu
                v-model="shapeMenu"
                :close-on-content-click="false"
                location="left"
              >
                <template v-slot:activator="{ props }">
                  <div>
                    <span style="color: white">Update Shape: </span>
                    <v-btn
                      color="#2a2d3300"
                      v-bind="props"
                      id="shapeButton"
                      size="x-large"
                    >
                      <v-icon
                        color="white"
                        v-show="getShapeIcon !== 'mdi-image'"
                      >
                        {{ getShapeIcon }}
                      </v-icon>
                      <v-img
                        v-show="getShapeIcon === 'mdi-image'"
                        width="40"
                        cover
                        :src="getSelectedElementType"
                      ></v-img>
                    </v-btn>
                  </div>
                </template>
                <v-card density="comfortable" color="#2a2d32ff">
                  <v-tabs v-model="tabs" bg-color="#2a2d32ff" fixed-tabs>
                    <v-tab value="classic" style="color: #cef8ff"
                      >Classic</v-tab
                    >
                    <v-tab value="web" style="color: #cef8ff">Web</v-tab>
                    <v-tab value="image" style="color: #cef8ff"
                      >Web Image</v-tab
                    >
                    <v-tab value="localImage" style="color: #cef8ff"
                      >Local Image</v-tab
                    >
                  </v-tabs>
                  <v-window v-model="tabs" style="background-color: #2a2d32ff">
                    <v-window-item value="classic">
                      <v-container>
                        <v-row justify="center" align="center">
                          <v-col cols="auto">
                            <v-btn
                              id="SQUARE"
                              icon="mdi-square-outline"
                              @click="updateElementShape('SQUARE')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="CIRCLE"
                              icon="mdi-circle-outline"
                              @click="updateElementShape('CIRCLE')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="TRIANGLE"
                              icon="mdi-triangle-outline"
                              @click="updateElementShape('TRIANGLE')"
                            />
                          </v-col>
                        </v-row>
                        <v-row justify="center" align="center">
                          <v-col cols="auto">
                            <v-btn
                              id="RHOMBUS"
                              icon="mdi-rhombus-outline"
                              @click="updateElementShape('RHOMBUS')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="STAR_5"
                              icon="mdi-star"
                              @click="updateElementShape('STAR_5')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="CYLINDER"
                              icon="mdi-cylinder"
                              @click="updateElementShape('CYLINDER')"
                            />
                          </v-col>
                        </v-row>
                        <v-row justify="center" align="center">
                          <v-col cols="auto">
                            <v-btn
                              id="PENTAGON"
                              icon="mdi-pentagon-outline"
                              @click="updateElementShape('PENTAGON')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="HEXAGON"
                              icon="mdi-hexagon-outline"
                              @click="updateElementShape('HEXAGON')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="OCTAGON"
                              icon="mdi-octagon-outline"
                              @click="updateElementShape('OCTAGON')"
                            />
                          </v-col>
                        </v-row>
                      </v-container>
                    </v-window-item>
                    <v-window-item value="web">
                      <v-container>
                        <v-row justify="center" align="center">
                          <v-col cols="auto">
                            <v-btn
                              id="DATABASE"
                              icon="mdi-database"
                              @click="updateElementShape('DATABASE')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="ENVELOPE"
                              icon="mdi-email-outline"
                              @click="updateElementShape('ENVELOPE')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="WORLD_WIRE"
                              icon="mdi-web"
                              @click="updateElementShape('WORLD_WIRE')"
                            />
                          </v-col>
                        </v-row>
                        <v-row justify="center" align="center">
                          <v-col cols="auto">
                            <v-btn
                              id="CLOUD"
                              icon="mdi-cloud-outline"
                              @click="updateElementShape('CLOUD')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="RSS"
                              icon="mdi-rss"
                              @click="updateElementShape('RSS')"
                            />
                          </v-col>
                          <v-col cols="auto">
                            <v-btn
                              id="SERVER"
                              icon="mdi-server"
                              @click="updateElementShape('SERVER')"
                            />
                          </v-col>
                        </v-row>
                      </v-container>
                    </v-window-item>
                    <v-window-item value="image">
                      <v-card-text style="color: white">
                        Please paste the desired URL in the box below
                      </v-card-text>
                      <v-text-field
                        :value="imageUrl"
                        v-model="imageUrl"
                        clearable
                        style="color: white"
                        placeholder="http://example.com/image.png"
                      />
                      <v-btn
                        color="#2a2d32ff"
                        style="color: white"
                        @click="updateElementShape(imageUrl)"
                      >
                        OK
                      </v-btn>
                    </v-window-item>
                    <v-window-item value="localImage">
                      <v-card-text style="color: white">
                        Select an image from your local files
                      </v-card-text>
                      <v-file-input
                        label="Click here to select image"
                        @change="handleImageUpload"
                        prepend-icon="mdi-image"
                      />
                      <v-btn
                        color="#2a2d33"
                        style="color: white"
                        @click="confirmLocalImage"
                        :disabled="
                          errorMessage !== '' || localImageUrl === null
                        "
                      >
                        OK
                      </v-btn>
                      <v-alert v-if="errorMessage" type="error" dismissible>
                        {{ errorMessage }}
                      </v-alert>
                    </v-window-item>
                  </v-window>
                </v-card>
              </v-menu>
            </div>

            <!-- New Subtabs for Color, Text, Effects -->
            <v-tabs
              v-model="elementsEditSubTab"
              bg-color="#2a2d32ff"
              fixed-tabs
            >
              <v-tab value="color" style="color: #fcf7f0">Color</v-tab>
              <v-tab value="text" style="color: #fcf7f0">Text</v-tab>
              <v-tab value="effects" style="color: #fcf7f0">Effects</v-tab>
            </v-tabs>
            <v-window
              v-model="elementsEditSubTab"
              style="background-color: #2a2d32ff"
            >
              <!-- Color Subtab -->
              <v-window-item value="color">
                <v-card color="#2a2d32ff" class="pa-2">
                  <v-card-text style="color: white">ELEMENT COLOR</v-card-text>
                  <v-color-picker
                    class="ma-2"
                    mode="hexa"
                    :model-value="
                      allSimpleElements.get(selectedElementsIds[0]).color
                    "
                    @update:model-value="updateElementColor($event)"
                    color="#2a2d32ff"
                  />
                  <v-card-text style="color: white">TEXT COLOR</v-card-text>
                  <v-color-picker
                    class="ma-2"
                    mode="hexa"
                    :model-value="
                      allSimpleElements.get(selectedElementsIds[0]).textColor
                    "
                    @update:model-value="updateElementTextColor($event)"
                    color="#2a2d32ff"
                  />
                </v-card>
              </v-window-item>

              <!-- Text Subtab -->
              <v-window-item value="text">
                <v-card color="#2a2d32ff" class="pa-4 mt-4">
                  <!-- Font Size -->
                  <div class="mb-4">
                    <div class="white--text mb-1">Font Size</div>
                    <v-select
                      :items="[
                        2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 40,
                        44, 48, 52, 56, 60, 64, 72, 80,
                      ]"
                      :value="
                        allSimpleElements.get(selectedElementsIds[0]).textSize
                      "
                      @update:model-value="updateElementTextSize"
                      variant="outlined"
                      density="compact"
                      bg-color="#333740"
                      hide-details
                      class="white--text"
                    ></v-select>
                  </div>

                  <!-- Font Family -->
                  <div class="mb-4">
                    <div class="white--text mb-1">Font Family</div>
                    <v-select
                      :items="fontFamilies"
                      :value="
                        allSimpleElements.get(selectedElementsIds[0]).textFont
                      "
                      @update:model-value="updateElementTextFont"
                      variant="outlined"
                      density="compact"
                      bg-color="#333740"
                      hide-details
                      class="white--text"
                      item-title="name"
                      item-value="value"
                    ></v-select>
                  </div>

                  <!-- Font Weight -->
                  <div class="mb-4">
                    <div class="white--text mb-1">Font Weight</div>
                    <v-select
                      :items="['normal', 'bold', 'lighter']"
                      :value="
                        allSimpleElements.get(selectedElementsIds[0]).textWeight
                      "
                      @update:model-value="updateElementTextWeight"
                      variant="outlined"
                      density="compact"
                      bg-color="#333740"
                      hide-details
                      class="white--text"
                    ></v-select>
                  </div>

                  <!-- Text Alignment (if applicable) -->
                  <v-card-text
                    class="white--text mt-4"
                    v-show="
                      allSimpleElements.get(selectedElementsIds[0]).type ===
                      'TEXT-CLASSIC'
                    "
                  >
                    Alignment:
                    {{
                      allSimpleElements.get(selectedElementsIds[0]).textAlign
                    }}
                  </v-card-text>
                  <v-btn-toggle
                    :value="
                      allSimpleElements.get(selectedElementsIds[0]).textAlign
                    "
                    @update:model-value="updateElementTextAlign($event)"
                    class="ma-2"
                    v-show="
                      allSimpleElements.get(selectedElementsIds[0]).type ===
                      'TEXT-CLASSIC'
                    "
                  >
                    <v-btn :value="'left'">
                      <v-icon>mdi-format-align-left</v-icon>
                    </v-btn>
                    <v-btn :value="'center'">
                      <v-icon>mdi-format-align-center</v-icon>
                    </v-btn>
                    <v-btn :value="'right'">
                      <v-icon>mdi-format-align-right</v-icon>
                    </v-btn>
                    <v-btn :value="'justify'">
                      <v-icon>mdi-format-align-justify</v-icon>
                    </v-btn>
                  </v-btn-toggle>
                </v-card>
              </v-window-item>

              <!-- Effects Subtab (empty for now) -->
              <v-window-item value="effects">
                <v-card color="#2a2d32ff" class="pa-4"> Coming Soon! </v-card>
              </v-window-item>
            </v-window>
          </v-card>
        </template>
      </v-window-item>

      <!-- LIST TAB (unchanged) -->
      <v-window-item value="list">
        <v-card color="#2a2d32ff">
          <v-card-title style="color: white">
            List of all Elements
          </v-card-title>
          <v-card
            v-for="(element, index) in getElements"
            :key="index"
            color="#2a2d32ff"
          >
            <v-btn @click="elementSelect(element.id)" color="#2a2d32ff">
              <v-chip
                :color="allSimpleElements.get(element.id).color"
                :variant="
                  selectedElementsIds.includes(element.id)
                    ? 'elevated'
                    : 'default'
                "
              >
                {{
                  element.name.length > 15
                    ? element.name.substring(0, 15) + "..."
                    : element.name
                }}
              </v-chip>
            </v-btn>
            <v-btn
              style="float: right"
              small
              color="#2a2d32ff"
              @click="elementDelete(element.id)"
            >
              <v-icon style="color: white"> mdi-delete </v-icon>
            </v-btn>
          </v-card>
        </v-card>
      </v-window-item>
    </v-window>
  </v-card>
</template>

<script>
import { mapActions, mapState } from "vuex";
import {
  addElementToSelection,
  configureElementAnchors,
  createWorkspace,
  deepClone,
  deleteElement,
  enterWorkspaceWithZoom,
  retraceAllSegments,
  updateElementDimensionsFromGraphic,
  updateGraphicElementFromState,
} from "@/core";
import { ELEMENT_POSITION, VIEWBOX } from "@/sequence";

export default {
  name: "ElementsTab",
  data() {
    return {
      // Existing data...
      drawer: false,
      editingElementName: false,
      editingTextContent: false,
      editingFrameContent: false,
      shapeMenu: false,
      tabs: "classic",
      imageUrl: null,
      isSmallScreen: false,
      frameDuration: 1,
      fontFamilies: [
        { name: "Arial", value: "Arial, sans-serif" },
        { name: "Courier New", value: '"Courier New", Courier, monospace' },
        { name: "Times New Roman", value: '"Times New Roman", Times, serif' },
        { name: "Georgia", value: "Georgia, serif" },
        { name: "Verdana", value: "Verdana, sans-serif" },
      ],
      // New inner subtab variable for the "edit" view
      elementsEditSubTab: "color",
      localImageUrl: null,
      errorMessage: "",
      tempElementName: "", // Temporary storage for element name during editing
    };
  },
  computed: {
    ...mapState({
      allSimpleElements: (state) => state.allSimpleElements,
      sequences: (state) => state.sequences,
      currentSequenceId: (state) => state.currentSequenceId,
      selectedElementsIds: (state) => state.selectedElementsIds,
      selectedTextsIds: (state) => state.selectedTextsIds,
      selectedSegmentsIds: (state) => state.selectedSegmentsIds,
      millisecondsPerProcessingUnit: (state) =>
        state.millisecondsPerProcessingUnit,
      diagramMode: (state) => state.diagramMode,
      snapshots: (state) => state.snapshots,
      recordingVideo: (state) => state.recordingVideo,
      showRightDrawer: (state) => state.showRightDrawer,
      selectedElements: (state) =>
        Array.from(state.allSimpleElements.values()).filter((element) =>
          state.selectedElementsIds.includes(element.id),
        ),
      headerTabStore: (state) => state.rightDrawerHeaderTab,
      elementsSubHeaderTabStore: (state) => state.rightDrawerElementsSubTab,
    }),
    currentSequence() {
      return this.sequences.get(this.currentSequenceId);
    },
    getElements() {
      return Array.from(this.allSimpleElements.values());
    },
    getShapeIcon() {
      const firstSelectedElement = this.selectedElements[0];
      if (firstSelectedElement.type === "SQUARE") return "mdi-square-outline";
      if (firstSelectedElement.type === "CIRCLE") return "mdi-circle-outline";
      if (firstSelectedElement.type === "TRIANGLE")
        return "mdi-triangle-outline";
      if (firstSelectedElement.type === "RHOMBUS") return "mdi-rhombus-outline";
      if (firstSelectedElement.type === "PENTAGON")
        return "mdi-pentagon-outline";
      if (firstSelectedElement.type === "HEXAGON") return "mdi-hexagon-outline";
      if (firstSelectedElement.type === "OCTAGON") return "mdi-octagon-outline";
      if (firstSelectedElement.type === "CYLINDER") return "mdi-cylinder";
      if (firstSelectedElement.type === "STAR_5") return "mdi-star";
      if (firstSelectedElement.type === "TEXT-CLASSIC")
        return "mdi-format-text";
      if (firstSelectedElement.type === "TEXT-STYLE")
        return "mdi-format-text-variant-outline";
      if (firstSelectedElement.type === "DATABASE") return "mdi-database";
      if (firstSelectedElement.type === "ENVELOPE") return "mdi-email-outline";
      if (firstSelectedElement.type === "RSS") return "mdi-rss";
      if (firstSelectedElement.type === "CLOUD") return "mdi-cloud-outline";
      if (firstSelectedElement.type === "SERVER") return "mdi-server";
      if (firstSelectedElement.type === "WORLD_WIRE") return "mdi-web";
      return "mdi-image";
    },
    getSelectedElementType() {
      return this.selectedElements[0].type;
    },
    headerTab: {
      get() {
        return this.headerTabStore;
      },
      set(value) {
        this.updateRightDrawerHeaderTabAction(value);
      },
    },
    elementsSubHeaderTab: {
      get() {
        return this.elementsSubHeaderTabStore;
      },
      set(value) {
        this.updateRightDrawerElementsSubTabAction(value);
      },
    },
    hasWorkspace() {
      if (this.selectedElementsIds.length !== 1) return false;
      const element = this.allSimpleElements.get(this.selectedElementsIds[0]);
      return element && element.childWorkspaceId !== null;
    },
  },
  methods: {
    ELEMENT_POSITION() {
      return ELEMENT_POSITION;
    },
    VIEWBOX() {
      return VIEWBOX;
    },
    ...mapActions([
      "updateSimpleElement",
      "updateDiagramModeAction",
      "updateSnapshotActive",
      "resetState",
      "updateSpeedAction",
      "updateRecordingVideoAction",
      "showRightDrawerAction",
      "updateRightDrawerHeaderTabAction",
      "updateRightDrawerElementsSubTabAction",
    ]),
    editElementName() {
      this.editingElementName = true;
      // Initialize temporary name with current name
      this.tempElementName = this.allSimpleElements.get(
        this.selectedElementsIds[0],
      ).name;
    },
    submitElementName() {
      this.updateElementName(this.tempElementName);
      this.editingElementName = false;
    },
    updateElement() {
      this.editingElementName = false;
      let elementId = this.selectedElementsIds[0];
      updateGraphicElementFromState(elementId);
      updateElementDimensionsFromGraphic(this.allSimpleElements.get(elementId));
      configureElementAnchors(elementId);
      retraceAllSegments(elementId);
    },
    updateElementColor(color) {
      let elementId = this.selectedElementsIds[0];
      const simpleElement = deepClone(this.allSimpleElements.get(elementId));
      simpleElement.color = color;
      this.updateSimpleElement({ simpleElement, undoable: true });
      updateGraphicElementFromState(elementId);
    },
    updateElementTextColor(color) {
      let elementId = this.selectedElementsIds[0];
      const simpleElement = deepClone(this.allSimpleElements.get(elementId));
      simpleElement.textColor = color;
      this.updateSimpleElement({ simpleElement, undoable: true });
      updateGraphicElementFromState(elementId);
    },
    updateElementName(name) {
      let elementId = this.selectedElementsIds[0];
      const simpleElement = deepClone(this.allSimpleElements.get(elementId));
      simpleElement.name = name;
      this.updateSimpleElement({ simpleElement, undoable: true });
      updateGraphicElementFromState(elementId);
    },
    updateElementTextSize(size) {
      let elementId = this.selectedElementsIds[0];
      const simpleElement = deepClone(this.allSimpleElements.get(elementId));
      simpleElement.textSize = size;
      this.updateSimpleElement({ simpleElement, undoable: true });
      updateGraphicElementFromState(elementId);
      updateElementDimensionsFromGraphic(simpleElement);
      configureElementAnchors(elementId);
      retraceAllSegments(elementId);
    },
    updateElementTextFont(font) {
      let elementId = this.selectedElementsIds[0];
      const simpleElement = deepClone(this.allSimpleElements.get(elementId));
      simpleElement.textFont = font;
      this.updateSimpleElement({ simpleElement, undoable: true });
      updateGraphicElementFromState(elementId);
      updateElementDimensionsFromGraphic(simpleElement);
      configureElementAnchors(elementId);
      retraceAllSegments(elementId);
    },
    updateElementTextWeight(weight) {
      let elementId = this.selectedElementsIds[0];
      const simpleElement = deepClone(this.allSimpleElements.get(elementId));
      simpleElement.textWeight = weight;
      this.updateSimpleElement({ simpleElement, undoable: true });
      updateGraphicElementFromState(elementId);
      updateElementDimensionsFromGraphic(simpleElement);
      configureElementAnchors(elementId);
      retraceAllSegments(elementId);
    },
    updateElementTextAlign(align) {
      let elementId = this.selectedElementsIds[0];
      const simpleElement = deepClone(this.allSimpleElements.get(elementId));
      simpleElement.textAlign = align;
      this.updateSimpleElement({ simpleElement, undoable: true });
      updateGraphicElementFromState(elementId);
    },
    async elementSelect(elementId) {
      await addElementToSelection({
        elementId,
        deselectOthers: true,
        undoable: false,
      });
    },
    async elementDelete(elementId) {
      await deleteElement({ elementId, undoable: true });
    },
    updateElementShape(shape) {
      const elementId = this.selectedElementsIds[0];
      const simpleElement = deepClone(this.allSimpleElements.get(elementId));
      simpleElement.type = shape;
      this.updateSimpleElement({ simpleElement, undoable: true });
      updateGraphicElementFromState(elementId);
    },
    handleImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Validate file type
      if (file.type.startsWith("image/")) {
        this.errorMessage = "";
      } else {
        this.errorMessage = "Please select a valid image file.";
        return;
      }

      const maxSize = 0.5 * 1024 * 1024; // 500kB
      if (file.size > maxSize) {
        this.errorMessage = "File size must be less than 5kB.";
        return;
      } else {
        this.errorMessage = "";
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        this.localImageUrl = e.target.result; // Store base64 image URL
      };
      reader.readAsDataURL(file);
    },
    confirmLocalImage() {
      if (this.localImageUrl) {
        this.updateElementShape(this.localImageUrl);
      }
    },
    adjustForScreenSize() {
      this.isSmallScreen = window.innerWidth < 768;
    },
    async handleWorkspaceAction() {
      if (this.selectedElementsIds.length !== 1) return;
      const elementId = this.selectedElementsIds[0];
      const element = this.allSimpleElements.get(elementId);

      if (element && element.childWorkspaceId) {
        await enterWorkspaceWithZoom(element.childWorkspaceId, elementId);
      } else {
        await createWorkspace(elementId);
        await enterWorkspaceWithZoom(element.childWorkspaceId, elementId);
      }
    },
  },
  mounted() {
    this.adjustForScreenSize();
    window.addEventListener("resize", this.adjustForScreenSize);
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.adjustForScreenSize);
  },
};
</script>

<style scoped>
.highlight {
  background-color: #e0f5ed;
}

.dark {
  background-color: #202226;
  color: white;
}

.dark-text {
  color: #202226;
}

.ivory-text {
  color: ivory;
}

.v-color-picker-edit__input input {
  background: yellow;
}

.create-workspace-button {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: white;
}

.create-workspace-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

.create-workspace-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
