<template>
  <div class="timeline-container">
    <!-- FIRST ROW: CONTROLS + SPEED -->
    <v-toolbar dense height="36" color="#2a2d32">
      <!-- Playback Controls & Frame -->
      <div class="timeline-header-section" style="width: 390px">
        <v-btn
          icon="mdi-skip-previous"
          @click="decrementFrame"
          :disabled="disableFrameNavigation || !this.currentSequence"
        ></v-btn>
        <v-btn
          :icon="currentSequence?.isPlaying ? 'mdi-pause' : 'mdi-play'"
          @click="toggle"
          id="timelinePlayerButton"
          :disabled="!this.currentSequence"
        ></v-btn>
        <v-btn
          icon="mdi-stop"
          @click="stop"
          :disabled="!this.currentSequence"
        ></v-btn>
        <v-btn
          icon="mdi-skip-next"
          @click="incrementFrame"
          :disabled="disableFrameNavigation || !this.currentSequence"
        ></v-btn>
        <v-btn
          @click="updateRepeat"
          id="timelinePlayerRepeatButton"
          :disabled="!this.currentSequence"
          ><v-icon
            v-bind:class="{
              menuButtonOn: this.repeat,
              menuButtonOff: !this.repeat,
            }"
          >
            mdi-repeat
          </v-icon></v-btn
        >
        <span class="subheading ml-3" v-show="this.currentSequence"
          >Frame {{ Math.round(currentSequenceFrame * 10) / 10 }}</span
        >
      </div>

      <v-divider vertical class="mx-4" v-show="expertMode"></v-divider>

      <!-- Sequence Selection Dropdown -->
      <div
        class="timeline-header-section"
        style="width: 10%"
        v-show="this.sequences.size > 1"
      >
        <v-select
          :items="getSequenceNames"
          :value="sequenceNameCurrentSelection"
          v-model="sequenceNameCurrentSelection"
          v-on:change="sequenceSelect"
          dense
          style="height: 60px"
          hide-details
          :disabled="this.currentSequence?.isPlaying"
        />
      </div>
    </v-toolbar>

    <!-- A relative container wrapping the entire timeline table -->
    <div
      class="timeline-wrapper-container"
      ref="timelineWrapper"
      style="position: relative"
    >
      <div class="timeline-table">
        <!-- Header Row -->
        <div class="timeline-table-row timeline-header-row">
          <div class="timeline-table-cell frame-column">
            <div class="timeline-header">
              <div
                v-for="(frame, index) in totalFrames"
                :key="'frame-header_' + index"
                class="timeline-header-frame"
                ref="timelineHeaderFrame"
                @mousemove="handleTimelineHeaderMousemove($event, index)"
                @mouseleave="hideHoverMarker"
                @click="jumpToFrame(index, $event)"
              >
                {{ index }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="timeline-marker"
        :style="markerStyle"
        @mousedown="startMarkerDrag"
        @mouseup="stopMarkerDrag"
      ></div>
      <div
        v-if="hoverMarkerVisible"
        class="timeline-hover-marker"
        :style="{ left: hoverMarkerPosition + 'px' }"
      ></div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from "vuex";
import {
  forceStopAnimation,
  pauseSequence,
  playFrame,
  playSequence,
  setupObjectsAtFrame,
  stopSequence,
} from "@/animation";
import { selectSequence, VIEWBOX } from "@/sequence";

export default {
  name: "SimpleTimelineComponent",
  data() {
    return {
      isDraggingMarker: false,
      markerStartX: 0,
      currentFrameOnDragStart: 0,
      isDraggingTimeline: false,
      startX: 0,
      scrollLeft: 0,
      editingSequenceName: false,
      expertMode: false,
      timelineDisplay: true,
      timelineHeaderFrameWidth: 100,
      speedMenu: false,
      hoverMarkerPosition: null,
      hoverMarkerVisible: false,
    };
  },
  mounted() {
    // Attach global listeners for drag
    window.addEventListener("mousemove", this.handleGlobalMouseMove);
    window.addEventListener("mouseup", this.handleGlobalMouseUp);
    this.$nextTick(() => {
      this.updateFrameWidth();
    });
  },
  beforeUnmount() {
    window.removeEventListener("mousemove", this.handleGlobalMouseMove);
    window.removeEventListener("mouseup", this.handleGlobalMouseUp);
  },
  computed: {
    ...mapState({
      allElements: (state) => state.allSimpleElements,
      currentSequence: (state) => state.sequences.get(state.currentSequenceId),
      sequences: (state) => state.sequences,
      currentSequenceId: (state) => state.currentSequenceId,
      millisecondsPerProcessingUnit: (state) =>
        state.millisecondsPerProcessingUnit,
      diagramMode: (state) => state.diagramMode,
      rifleModeState: (state) => state.rifleMode,
      immersiveViewState: (state) => state.immersiveView,
      repeat: (state) => state.repeat,
    }),
    currentSequenceFrame: {
      get() {
        return this.currentSequence ? this.currentSequence.currentFrame : 0;
      },
      set(value) {
        this.setCurrentFrameForCurrentSequenceAction({
          currentFrame: value,
          sequenceId: this.currentSequenceId,
        });
      },
    },
    getFramesPerSecond: {
      get() {
        return (
          Math.round((1000 / this.millisecondsPerProcessingUnit) * 100) / 100
        );
      },
      set(value) {
        this.updateSpeedAction({ millisecondsPerProcessingUnit: 1000 / value });
      },
    },
    totalFrames() {
      return this.currentSequence?.frames.length || 0;
    },
    markerStyle() {
      if (!this.currentSequence || this.totalFrames === 0) {
        return { left: "0" };
      }

      const markerPosition =
        this.currentSequence.currentFrame * this.timelineHeaderFrameWidth;
      return {
        left: `${markerPosition}px`,
        transition:
          this.isDraggingMarker || this.currentSequence.currentFrame === 0
            ? "none"
            : this.currentSequence?.isPlaying
              ? `left 0.5s linear`
              : "left 0.15s linear",
      };
    },

    sequenceNameCurrentSelection: {
      get() {
        return this.currentSequence ? this.currentSequence.name : null;
      },
      set(value) {
        const seq = Array.from(this.sequences.values()).find(
          (sequence) => sequence.name === value,
        );
        if (seq) {
          this.sequenceSelect(seq.id);
        }
      },
    },
    viewIsSet() {
      const currentSequence = this.sequences?.get(this.currentSequenceId);
      const viewIsSet = currentSequence?.frames?.some((frame) =>
        frame.some((action) => action.metadata.type === VIEWBOX),
      );
      if (!viewIsSet) {
        this.toggleImmersiveViewAction(true);
      }
      return viewIsSet;
    },
    sendDataIsSet() {
      const currentSequence = this.sequences?.get(this.currentSequenceId);
      return currentSequence?.frames?.some((frame) =>
        frame.some((action) => action.metadata.type === "sendData"),
      );
    },
    disableFrameNavigation() {
      return (
        !this.currentSequence ||
        (this.currentSequence.isPlaying && !this.currentSequence.isPaused)
      );
    },
    getSequenceNames() {
      return Array.from(this.sequences.values()).map(
        (sequence) => sequence.name,
      );
    },
  },
  methods: {
    ...mapActions([
      "setCurrentFrameForCurrentSequenceAction",
      "updateSpeedAction",
      "toggleRifleModeAction",
      "toggleImmersiveViewAction",
      "updateSequenceAction",
      "updateRepeatAction",
    ]),
    updateFrameWidth() {
      const timelineHeaderFrame = this.$refs.timelineHeaderFrame?.at(0);
      if (timelineHeaderFrame) {
        this.timelineHeaderFrameWidth = timelineHeaderFrame.clientWidth || 0;
      }
    },
    sequenceSelect(sequenceId) {
      selectSequence(sequenceId);
      setupObjectsAtFrame(
        sequenceId,
        this.sequences.get(this.currentSequenceId).currentFrame,
      );
    },
    startMarkerDrag(e) {
      e.stopPropagation();
      this.isDraggingMarker = true;

      // Record initial conditions
      this.markerStartX = e.pageX;
      this.currentFrameOnDragStart = this.currentSequenceFrame;
    },

    stopMarkerDrag() {
      this.currentSequenceFrame =
        Math.round(this.currentSequenceFrame * 10) / 10;
    },

    handleGlobalMouseMove(e) {
      if (this.isDraggingMarker) {
        this.dragMarker(e);
      } else if (this.isDragging) {
        // If we are dragging timeline (if you keep that feature)
        this.handleTimelineScroll(e);
      }
    },

    handleGlobalMouseUp() {
      this.isDraggingMarker = false;
      this.isDragging = false; // Also stop timeline dragging if that was ongoing
    },

    dragMarker(e) {
      if (!this.currentSequence || this.totalFrames === 0) return;

      const deltaX = e.pageX - this.markerStartX;

      const deltaFrames = deltaX / this.timelineHeaderFrameWidth;

      // Instead of flooring, just use the fractional value
      let newFrame = this.currentFrameOnDragStart + deltaFrames;

      // Clamp newFrame to valid range, but do not round it
      if (newFrame < 0) newFrame = 0;
      if (newFrame > this.totalFrames - 1) newFrame = this.totalFrames - 1;

      this.currentSequenceFrame = newFrame;
      forceStopAnimation();
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
      );
    },
    handleTimelineScroll(e) {
      if (!this.isDragging) return;
      const x = e.pageX - this.$refs.timelineWrapper.offsetLeft;
      const walk = x - this.startX;
      this.$refs.timelineWrapper.scrollLeft = this.scrollLeft - walk;
    },

    getName(elementId) {
      return this.allElements.get(elementId)?.name || "...";
    },

    toggle() {
      if (this.currentSequence.isPlaying) {
        pauseSequence(this.currentSequenceId);
      } else {
        playSequence(this.currentSequenceId);
      }
    },
    stop() {
      stopSequence(this.currentSequenceId);
    },
    incrementFrame() {
      let currentSpeed = this.millisecondsPerProcessingUnit;
      let frameTicksElapsed =
        Math.round((this.currentSequence.currentFrame % 1) * 10) / 10;
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: 500,
        undoable: false,
      });
      this.currentSequence.currentFrame = Math.floor(
        this.currentSequence.currentFrame + 1,
      );
      this.currentSequence.isPlaying = false;
      forceStopAnimation();
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
        true,
      );
      playFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
        null,
        frameTicksElapsed,
        true,
      );
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: currentSpeed,
        undoable: false,
      });
    },
    decrementFrame() {
      if (this.currentSequence.currentFrame <= 0) {
        return;
      }
      let currentSpeed = this.millisecondsPerProcessingUnit;
      let frameTicksElapsed =
        Math.round((this.currentSequence.currentFrame % 1) * 10) / 10;
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: 500,
        undoable: false,
      });
      if (frameTicksElapsed === 0) {
        this.currentSequence.currentFrame--;
      } else {
        this.currentSequence.currentFrame = Math.floor(
          this.currentSequence.currentFrame,
        );
      }
      this.currentSequence.isPlaying = false;
      forceStopAnimation();
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
        true,
      );
      playFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame + 1,
        true,
        frameTicksElapsed === 0 ? 1 : frameTicksElapsed,
        true,
      );
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: currentSpeed,
        undoable: false,
      });
    },
    updateRepeat() {
      this.updateRepeatAction(!this.repeat);
    },
    handleTimelineHeaderMousemove(event) {
      const wrapperRect = this.$refs.timelineWrapper.getBoundingClientRect();

      // mouse position relative to timeline wrapper container
      const cursorX =
        event.clientX -
        wrapperRect.left +
        this.$refs.timelineWrapper.scrollLeft;

      // Set the hover marker exactly under the cursor
      this.hoverMarkerPosition = cursorX;
      this.hoverMarkerVisible = true;
    },

    hideHoverMarker() {
      this.hoverMarkerVisible = false;
    },

    jumpToFrame(frameIndex, event) {
      const frameRect = event.currentTarget.getBoundingClientRect();
      const relativeX = event.clientX - frameRect.left;
      const fraction = relativeX / frameRect.width;

      this.currentSequenceFrame = parseFloat(
        (frameIndex + fraction).toFixed(1),
      );
      forceStopAnimation();
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequenceFrame,
        true,
      );
    },
  },
};
</script>

<style scoped>
.resize-handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px; /* Handle size */
  background: #444;
  cursor: ns-resize; /* Resize cursor */
  z-index: 1001;
}

.timeline-container {
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
  left: 60px;
  right: 0;
  background-color: #2a2d32;
  border-top: 2px solid #444;
  padding: 10px;
  z-index: 1000;
  box-sizing: border-box;
  margin: 0 !important;
  /* Prevent text selection in the entire timeline area */
  user-select: none;
  -webkit-user-select: none; /* For Safari */
  -ms-user-select: none; /* For older IE/Edge */
}

.timeline-hover-marker {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: rgba(255, 0, 0, 0.2);
  pointer-events: none;
  z-index: 20;
}

.checkboxes-row {
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.speed-control {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.menuButtonOn {
  color: #6cffd8 !important;
}

.menuButtonOff {
  color: white !important;
}

.timeline-wrapper-container {
  position: relative;
  overflow-x: auto;
  white-space: nowrap;
}

/* Example styling if you have a timeline table and rows: */
.timeline-table {
  display: table;
  width: 100%;
  border-collapse: collapse;
  position: relative;
}

.timeline-table-row {
  display: table-row;
}

.timeline-table-cell {
  display: table-cell;
  vertical-align: top;
  border-bottom: 1px solid #555;
}

.frame-column {
  /* For timeline frames and actions */
}

.timeline-header {
  display: flex;
  border-top: 1px solid #555;
}

.timeline-header-frame {
  width: 100px;
  text-align: left;
  color: #ddd;
  font-size: 12px;
  padding: 5px;
  border-left: 1px solid #444;
  cursor: crosshair; /* Resize cursor */
}

.timeline-row {
  display: flex;
  height: 30px;
  align-items: center;
}

.timeline-cell {
  width: 100px;
  position: relative;
}

.timeline-marker {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: red;
  z-index: 10;
  cursor: col-resize;
}

.timeline-marker::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 20px;
  background: transparent;
  transform: translateX(-50%);
  pointer-events: auto;
}

.timeline-marker:hover::before {
  background: rgba(255, 0, 0, 0.2);
  border-radius: 4px;
}

.timeline-header-section {
  margin: 0 10px;
}

.hoverable {
  position: relative;
  padding-right: 24px; /* Add some space for the delete icon if needed */
}

/* Hide the delete icon by default */
.delete-icon {
  display: none;
  position: absolute;
  top: 50%;
  right: 2px;
  transform: translateY(-50%);
  background: transparent;
  color: red;
}

/* Show the delete icon on action hover */
.hoverable:hover .delete-icon {
  display: inline-flex;
}

.v-btn--icon.v-btn--density-default {
  width: 50px;
}

.timeline-edit-sequence {
  display: flex;
  align-items: center;
  padding: 0 10px;
  background-color: #2a2d32;
  border-top: 1px solid #444;
  height: 36px; /* Matches the toolbar height */
}

.timeline-edit-sequence v-text-field {
  margin: 0;
  padding: 0;
}

@media (max-width: 1200px) {
  .v-btn--icon.v-btn--density-default {
    width: 25px;
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .timeline-container {
    padding: 5px;
    left: 50px;
  }

  .timeline-header-frame {
    width: 50px; /* Smaller frame width for mobile */
    font-size: 9px;
  }

  .icon-column {
    width: 40px; /* Adjust icon column width */
  }

  .hoverable {
    padding-right: 16px; /* Adjust spacing for delete icon */
  }

  .delete-icon {
    right: 0; /* Align delete icon closer on mobile */
  }

  .v-btn--icon.v-btn--density-default {
    width: 20px;
  }
}

@media (max-width: 480px) {
  .timeline-container {
    padding: 2px;
  }

  .timeline-header-frame {
    width: 40px;
    font-size: 8px;
  }
}
</style>
