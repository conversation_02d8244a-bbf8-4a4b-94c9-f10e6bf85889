<template>
  <v-dialog width="600" height="800" class="dialog" persistent>
    <v-card color="#2e2e2e">
      <v-card-title>
        <span class="headline">Alpha Version Feedback</span>
      </v-card-title>
      <v-card-text>
        <v-form ref="feedbackForm" lazy-validation>
          <v-text-field
            v-model="feedbackName"
            :rules="nameRules"
            label="Your Name (optional)"
            outlined
          ></v-text-field>
          <v-textarea
            v-model="feedbackLikes"
            :rules="textRules"
            label="What did you enjoy the most?"
            outlined
            rows="3"
          ></v-textarea>
          <v-textarea
            v-model="feedbackDislikes"
            :rules="textRules"
            label="What should be improved?"
            outlined
            rows="3"
          ></v-textarea>
          <v-textarea
            v-model="feedbackBugs"
            :rules="textRules"
            label="Any bugs to report? (Please include the steps to reproduce)"
            outlined
            rows="3"
          ></v-textarea>
          <v-textarea
            v-model="wishlist"
            :rules="textRules"
            label="What features would you like us to develop?"
            outlined
            rows="3"
          ></v-textarea>
        </v-form>
        <!-- Error message alert -->
        <v-alert v-if="errorMessage" type="error" dismissible>
          {{ errorMessage }}
        </v-alert>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="cancel" :disabled="isLoading">Cancel</v-btn>
        <v-btn color="primary" @click="submitFeedback" :disabled="isLoading">
          <template v-if="!isLoading"> Submit </template>
          <template v-else>
            <v-progress-circular
              indeterminate
              color="white"
              size="20"
              width="2"
            ></v-progress-circular>
          </template>
        </v-btn>
      </v-card-actions>
    </v-card>
    <v-overlay :value="isLoading" absolute opacity="0.3">
      <v-progress-circular
        indeterminate
        color="primary"
        size="64"
      ></v-progress-circular>
    </v-overlay>
  </v-dialog>
</template>

<script>
import { sendFeedback } from "@/aws-config";

export default {
  name: "FeedbackDialog",
  data() {
    return {
      feedbackName: "",
      feedbackLikes: "",
      feedbackDislikes: "",
      feedbackBugs: "",
      wishlist: "",
      isLoading: false, // Controls the spinner overlay
      errorMessage: "", // Holds error message to display in the modal
      // Validation rules:
      nameRules: [
        // Allow empty or up to 20 characters:
        (v) => !v || v.length <= 20 || "Name must be at most 20 characters",
      ],
      textRules: [
        // Allow empty or up to 4000 characters:
        (v) =>
          !v ||
          v.length <= 4000 ||
          "This field must be at most 4000 characters",
      ],
    };
  },
  methods: {
    // Cross-field validation: Ensure that at least one non-name field has at least 10 characters (after trimming)
    validateCrossField() {
      const fields = [
        this.feedbackLikes,
        this.feedbackDislikes,
        this.feedbackBugs,
        this.wishlist,
      ];
      return fields.some((field) => field.trim().length >= 10);
    },
    submitFeedback() {
      // Clear previous error messages
      this.errorMessage = "";

      // Validate field-level rules using the v-form
      const valid = this.$refs.feedbackForm.validate();
      if (!valid) {
        this.errorMessage = "Please fix the errors in the form.";
        return;
      }

      // --- Manual checks for maximum length ---
      if (this.feedbackLikes && this.feedbackLikes.length > 4000) {
        this.errorMessage =
          "The field 'What did you enjoy the most?' must be at most 4000 characters.";
        return;
      }
      if (this.feedbackDislikes && this.feedbackDislikes.length > 4000) {
        this.errorMessage =
          "The field 'What should be improved?' must be at most 4000 characters.";
        return;
      }
      if (this.feedbackBugs && this.feedbackBugs.length > 4000) {
        this.errorMessage =
          "The field 'Any bugs to report?' must be at most 4000 characters.";
        return;
      }
      if (this.wishlist && this.wishlist.length > 4000) {
        this.errorMessage =
          "The field 'What features would you like us to develop?' must be at most 4000 characters.";
        return;
      }
      // --- End Manual Checks ---

      // Check cross-field rule: At least one non-name field must have at least 10 characters
      if (!this.validateCrossField()) {
        this.errorMessage =
          "At least one of the fields (except name) must be filled with at least 10 characters.";
        return;
      }

      // Build the feedback object
      const feedbackData = {
        name: this.feedbackName,
        likes: this.feedbackLikes,
        dislikes: this.feedbackDislikes,
        bugs: this.feedbackBugs,
        wishlist: this.wishlist,
      };

      // Start the spinner and attempt to send feedback
      this.isLoading = true;
      sendFeedback(feedbackData)
        .then(() => {
          this.isLoading = false;
          // Emit success event (handled by the parent component) before closing the modal
          this.$emit("feedbackSuccess", "Feedback submitted successfully!");
          this.$emit("updateFeedbackDialog", false);
          // Reset the form fields
          this.feedbackName = "";
          this.feedbackLikes = "";
          this.feedbackDislikes = "";
          this.feedbackBugs = "";
          this.wishlist = "";
        })
        .catch((error) => {
          this.isLoading = false;
          this.errorMessage = "Error sending feedback. Please try again later.";
          console.error("Error sending feedback:", error);
        });
    },
    cancel() {
      if (!this.isLoading) {
        this.$emit("updateFeedbackDialog", false);
        this.errorMessage = "";
      }
    },
  },
};
</script>

<style scoped>
.dialog {
  background-color: rgba(145, 150, 158, 0.6);
  backdrop-filter: blur(2px);
}
</style>
