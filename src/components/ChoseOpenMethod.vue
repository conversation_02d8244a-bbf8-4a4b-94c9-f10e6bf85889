<template>
  <v-dialog width="640">
    <v-card color="#1c1f22">
      <v-card-title style="text-align: center">
        <span class="text-h5">Open file from</span>
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12" md="6">
              <v-btn
                @click="browser"
                height="100px"
                width="100%"
                color="#6d949b"
                ><v-icon size="50">mdi-television</v-icon>
                Browser
              </v-btn>
            </v-col>
            <v-col cols="12" md="6">
              <v-btn
                @click="filesystem"
                height="100px"
                width="100%"
                color="#6d949b"
                ><v-icon size="50">mdi-file-export</v-icon>
                My Files
              </v-btn>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions } from "vuex";
import { loadWorkspaceFromDeserialisedContent } from "@/core";

export default {
  name: "ChoseOpenMethod",
  methods: {
    ...mapActions([
      "updateSpeedAction",
      "updateDiagramNameAction",
      "setLatestSnapshotToNotDirtyAction",
    ]),
    browser() {
      this.$emit("updateOpenFromLocalStorageDialog", true);
      this.$emit("updateChoseOpenMethodDialog", false);
    },
    async filesystem() {
      let fileHandle;
      if (window.showOpenFilePicker) {
        // Modern File System API (Chrome, Edge)
        try {
          [fileHandle] = await window.showOpenFilePicker();
          const file = await fileHandle.getFile();
          const content = await file.text();
          const success = await loadWorkspaceFromDeserialisedContent(
            content,
            "Unable to open that project. Make sure it is valid.",
          );

          if (success) {
            this.$emit("updateChoseOpenMethodDialog", false);
          }
          return;
        } catch (e) {
          console.warn("File selection aborted or failed:", e);
        }
      }

      // Fallback for Safari (uses input type=file)
      return new Promise((resolve) => {
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "*"; // You can specify accepted file types if needed

        input.onchange = async (event) => {
          const file = event.target.files[0];
          if (!file) return;

          const content = await file.text();
          const success = await loadWorkspaceFromDeserialisedContent(
            content,
            "Unable to open that project. Make sure it is valid.",
          );

          if (success) {
            this.$emit("updateChoseOpenMethodDialog", false);
          }

          resolve();
        };

        input.click(); // Simulate a click to open the file picker
      });
    },
  },
  components: {},
  computed: {},
  data: function () {
    return {};
  },
};
</script>
<style scoped></style>
