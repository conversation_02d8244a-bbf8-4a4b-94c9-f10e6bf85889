<template>
  <v-navigation-drawer
    location="right"
    color="#2a2d32ff"
    :width="isSmallScreen ? 150 : 300"
    v-model="this.showRightDrawer"
  >
    <v-btn
      icon
      @click.stop="toggleShowRightDrawer"
      fab
      style="background-color: #2a2d32ff"
    >
      <v-icon color="white">mdi-chevron-right</v-icon>
    </v-btn>
    <v-tabs v-model="headerTab" bg-color="#2a2d32ff" fixed-tabs>
      <v-tab value="elements" style="color: #cef8ff">Elements</v-tab>
      <v-tab
        value="sequences"
        style="color: #cef8ff"
        v-show="sequences.size > 0"
        >Sequences</v-tab
      >
      <v-tab value="workspaces" style="color: #cef8ff">Workspaces</v-tab>
    </v-tabs>
    <v-window v-model="headerTab" style="background-color: #2a2d32ff">
      <v-window-item value="elements">
        <ElementsTab />
      </v-window-item>
      <v-window-item value="sequences">
        <SequencesTab />
      </v-window-item>
      <v-window-item value="workspaces">
        <Workspaces />
      </v-window-item>
    </v-window>
  </v-navigation-drawer>
</template>

<script>
import { mapActions, mapState } from "vuex";
import { selectSequence } from "@/sequence";
import { setupObjectsAtFrame } from "@/animation";
import ElementsTab from "@/components/ElementsTab.vue";
import Workspaces from "./Workspaces.vue";
import SequencesTab from "./SequencesTab.vue";

export default {
  name: "RightDrawer",
  methods: {
    ...mapActions([
      "showRightDrawerAction",
      "updateRightDrawerHeaderTabAction",
      "updateRightDrawerElementsSubTabAction",
    ]),
    toggleShowRightDrawer() {
      this.showRightDrawerAction(!this.showRightDrawer);
    },
    sequenceSelect(sequenceId) {
      selectSequence(
        sequenceId ||
          Array.from(this.sequences.values()).find(
            (sequence) => sequence.name === this.textSequenceCurrentSelection,
          ).id,
      );
      setupObjectsAtFrame(
        sequenceId,
        this.sequences.get(this.currentSequenceId).currentFrame,
      );
    },
    adjustForScreenSize() {
      this.isSmallScreen = window.innerWidth < 768;
    },
  },
  mounted() {
    this.adjustForScreenSize();
    window.addEventListener("resize", this.adjustForScreenSize);
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.adjustForScreenSize);
  },
  components: {
    ElementsTab,
    SequencesTab,
    Workspaces,
  },
  computed: {
    ...mapState({
      allSimpleElements: (state) => state.allSimpleElements,
      sequences: (state) => state.sequences,
      currentSequenceId: (state) => state.currentSequenceId,
      selectedElementsIds: (state) => state.selectedElementsIds,
      selectedTextsIds: (state) => state.selectedTextsIds,
      selectedSegmentsIds: (state) => state.selectedSegmentsIds,
      millisecondsPerProcessingUnit: (state) =>
        state.millisecondsPerProcessingUnit,
      diagramMode: (state) => state.diagramMode,
      snapshots: (state) => state.snapshots,
      recordingVideo: (state) => state.recordingVideo,
      showRightDrawer: (state) => state.showRightDrawer,
      selectedElements: (state) =>
        Array.from(state.allSimpleElements.values()).filter((element) =>
          state.selectedElementsIds.includes(element.id),
        ),
      headerTabStore: (state) => state.rightDrawerHeaderTab,
      elementsSubHeaderTabStore: (state) => state.rightDrawerElementsSubTab,
    }),
    currentSequence() {
      return this.sequences.get(this.currentSequenceId);
    },
    getFirstSelectedTextId() {
      return this.selectedTextsIds.values().next().value;
    },
    textSequenceCurrentSelection: {
      get: function () {
        return this.currentSequence ? this.currentSequence.name : null;
      },
      set: function (value) {
        this.sequenceSelect(
          Array.from(this.sequences.values()).find(
            (sequence) => sequence.name === value,
          ).id,
        );
      },
    },
    getTextCurrentSequenceDisplayOnFrame: {
      get: function () {
        return this.allSimpleTexts.get(this.getFirstSelectedTextId).sequences
          ? this.allSimpleTexts
              .get(this.getFirstSelectedTextId)
              .sequences.get(this.currentSequenceId)
            ? this.allSimpleTexts
                .get(this.getFirstSelectedTextId)
                .sequences.get(this.currentSequenceId).displayOnFrame
            : null
          : null;
      },
      set: function (value) {
        this.currentSequenceDisplayOnFrame = value;
      },
    },
    getTextCurrentSequenceDisplayOffFrame: {
      get: function () {
        return this.allSimpleTexts.get(this.getFirstSelectedTextId).sequences
          ? this.allSimpleTexts
              .get(this.getFirstSelectedTextId)
              .sequences.get(this.currentSequenceId)
            ? this.allSimpleTexts
                .get(this.getFirstSelectedTextId)
                .sequences.get(this.currentSequenceId).displayOffFrame
            : null
          : null;
      },
      set: function (value) {
        this.currentSequenceDisplayOffFrame = value;
      },
    },
    headerTab: {
      get: function () {
        return this.headerTabStore;
      },
      set: function (value) {
        this.updateRightDrawerHeaderTabAction(value);
      },
    },
    elementsSubHeaderTab: {
      get: function () {
        return this.elementsSubHeaderTabStore;
      },
      set: function (value) {
        this.updateRightDrawerElementsSubTabAction(value);
      },
    },
  },
  data: function () {
    return {
      drawer: false,
      editingSequenceName: false,
      editingElementName: false,
      editingTextContent: false,
      editingFrameContent: false,
      currentSequenceDisplayOffFrame: null,
      currentSequenceDisplayOnFrame: null,
      shapeMenu: false,
      tabs: "classic",
      imageUrl: null,
      isSmallScreen: false,
      frameDuration: 1,
      fontFamilies: [
        { name: "Arial", value: "Arial, sans-serif" },
        { name: "Courier New", value: '"Courier New", Courier, monospace' },
        { name: "Times New Roman", value: '"Times New Roman", Times, serif' },
        { name: "Georgia", value: "Georgia, serif" },
        { name: "Verdana", value: "Verdana, sans-serif" },
      ],
    };
  },
};
</script>
<style scoped>
.highlight {
  background-color: #e0f5ed;
}

.dark {
  background-color: #202226;
  color: white;
}

.dark-text {
  color: #202226;
}

.ivory-text {
  color: ivory;
}

.v-color-picker-edit__input input {
  background: yellow;
}
</style>
