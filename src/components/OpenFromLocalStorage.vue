<template>
  <v-dialog width="1024">
    <v-card color="#1c1f22">
      <v-card-title> Select File </v-card-title>
      <v-card-text>
        <v-container>
          <v-table
            fixed-header
            height="512px"
            style="background-color: #1c1f22"
          >
            <thead>
              <tr>
                <th class="text-left">Name</th>
                <th class="text-left">Delete</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in fileList" :key="item.fileName">
                <td>
                  <v-btn
                    color="#6d949b"
                    variant="flat"
                    @click="loadFile(item.fileName)"
                    >{{ item.fileName }}</v-btn
                  >
                </td>
                <td>
                  <v-btn
                    color="#555555"
                    @click="deleteFile(item.fileName)"
                    icon="mdi-delete"
                  ></v-btn>
                </td>
              </tr>
            </tbody>
          </v-table>
        </v-container>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import { saveTypes, setSaveDafault } from "@/files";
import { mapActions } from "vuex";
import { loadWorkspaceFromDeserialisedContent } from "@/core";

export default {
  name: "OpenFromLocalStorage",
  updated() {
    this.updateFileList();
  },
  methods: {
    ...mapActions([
      "updateSpeedAction",
      "updateDiagramNameAction",
      "setLatestSnapshotToNotDirtyAction",
    ]),
    updateFileList() {
      this.fileList = [];
      for (let i = 0; i < localStorage.length; i++) {
        let skip = false;
        const fileName = localStorage.key(i);
        const fileDataString = localStorage.getItem(fileName);
        let fileData;
        try {
          fileData = JSON.parse(fileDataString);
        } catch (e) {
          skip = true;
        }
        if (skip) {
          continue;
        }
        this.fileList.push({
          fileName,
          lastUpdated: new Date(fileData.lastUpdated).toLocaleString(),
          size: new TextEncoder().encode(fileData.content).length,
        });
      }
    },
    async loadFile(fileName) {
      const fileContent = localStorage.getItem(fileName);
      const success = await loadWorkspaceFromDeserialisedContent(
        fileContent,
        "Unable to open that project. Make sure it is valid.",
      );
      if (success) {
        setSaveDafault(saveTypes.browser);
        this.$emit("updateOpenFromLocalStorageDialog", false);
      }
    },
    deleteFile(fileName) {
      localStorage.removeItem(fileName);
      this.updateFileList();
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
};
</script>
<style>
.text-left {
  width: 90%;
  color: white;
  background-color: #1c1f22 !important;
}
</style>
