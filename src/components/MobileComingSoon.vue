<template>
  <v-dialog
    max-width="400"
    max-height="300"
    scrollable
    persistent
    transition="slide-x-transition"
    no-click-animation
    class="dialog"
  >
    <v-card color="#1c1f22" elevation="0">
      <v-card-title class="headline text-center">
        Mobile Version Coming Soon!
      </v-card-title>
      <v-card-text class="text-center">
        We’re working hard on a mobile‑optimized experience. Please use a
        desktop browser in the meantime.
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: "MobileComingSoon",
  data() {},
};
</script>

<style scoped>
.dialog {
  background-color: rgba(145, 150, 158, 0.6);
  backdrop-filter: blur(2px);
}
</style>
