<template>
  <div id="svg-container" />
</template>

<script>
import { initialise, setup } from "@/core";
import { mapState } from "vuex";

export default {
  name: "SVGMain",
  computed: {
    ...mapState(["diagramMode"]),
  },
  methods: {
    createWorkspace: function () {
      initialise();
      setup();
    },
  },
  mounted() {
    this.createWorkspace();
  },
};
</script>
<style scoped></style>
