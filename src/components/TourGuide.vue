<template>
  <div>
    <div :style="overlayStyle">
      <div v-if="targetElement" :style="spotlightStyle"></div>
      <v-card variant="outlined" :style="cardStyle">
        <v-card-title
          style="font-weight: bold; font-size: x-large; color: #0c53bd"
        >
          {{ currentStepInfo.title }}
        </v-card-title>

        <v-card-text
          style="white-space: normal; font-weight: bold; font-size: large"
          v-html="currentStepInfo.content"
        >
        </v-card-text>

        <v-card-actions>
          <v-row>
            <v-col cols="12" sm="6" class="d-flex justify-start">
              <v-btn
                @click="prevStep"
                variant="outlined"
                color="#000000"
                size="small"
                :disabled="currentStep === 0"
              >
                Previous
              </v-btn>
              <v-btn
                @click="nextStep"
                variant="outlined"
                color="#000000"
                size="small"
                :disabled="currentStep === tourSteps.length - 1"
              >
                Next
              </v-btn>
            </v-col>
            <v-col cols="12" sm="6" class="d-flex justify-end">
              <v-btn
                @click="completeTour"
                variant="outlined"
                color="#000000"
                size="small"
              >
                Close
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>

        <v-card-actions>
          <v-row>
            <v-col cols="12" class="text-center">
              <v-checkbox
                v-model="dontShowAgain"
                label="Don't show on startup"
                density="compact"
                color="#000000"
              ></v-checkbox>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-card>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";

export default {
  name: "TourGuide",
  data() {
    return {
      currentStep: 0,
      dontShowAgain: false,
      targetElement: null,
      overlayStyle: {
        position: "fixed",
        top: "0",
        left: "0",
        width: "100%",
        height: "100%",
        background: "rgba(0, 0, 0, 0)",
        zIndex: "1500",
        pointerEvents: "none",
      },
      tourSteps: [
        {
          id: 1,
          title: "Important Info (1/5)",
          content:
            "Double-click anywhere on the canvas to create a new element.",
          targetSelector: null,
          position: { top: "50%", left: "70%" },
        },
        {
          id: 2,
          title: "Important Info (2/5)",
          content:
            "Double-click on an element to rename it and adjust name position.",
          targetSelector: "#svgDrawingArea",
          position: { top: "45%", left: "65%" },
        },
        {
          id: 3,
          title: "Edit Properties (3/5)",
          content: "Use the right panel to modify the element's  properties.",
          targetSelector: ".v-navigation-drawer--right",
          position: { top: "50%", left: "70%" },
        },
        {
          id: 4,
          title: "Choose a Shape (4/5)",
          content:
            "Press the shape button located in the toolbar to choose a shape for your element. You can either drag it onto the grid or simply select it and double-click anywhere on the grid.",
          targetSelector: "#shapeButton",
          position: { top: "40%", left: "20%" },
        },
        {
          id: 5,
          title: "Connect Elements (5/5)",
          content:
            "To connect elements, drag a connector from one element to the center of another. If you drag it to an empty area, a new connected element will be created.",
          targetSelector: null,
          position: { top: "50%", left: "70%" },
        },
        {
          id: 6,
          title: "Well Done!",
          content:
            "For more tips, check out the guided tutorial in the Help menu.",
          targetSelector: null,
          position: { top: "50%", left: "50%" },
        },
      ],
    };
  },
  computed: {
    currentStepInfo() {
      return this.tourSteps[this.currentStep];
    },
    spotlightStyle() {
      if (!this.targetElement) return {};

      const rect = this.targetElement.getBoundingClientRect();
      return {
        position: "absolute",
        top: `${rect.top}px`,
        left: `${rect.left}px`,
        width: `${rect.width}px`,
        height: `${rect.height}px`,
        borderRadius: "4px",
        pointerEvents: "none",
        zIndex: "1600",
        backgroundColor: "transparent",
        border: "2px solid #8eff7d",
      };
    },
    cardStyle() {
      // Dynamically set the position based on the current step
      const position = this.currentStepInfo.position;
      return {
        textAlign: "center",
        width: "400px",
        position: "absolute",
        top: position.top,
        left: position.left,
        transform: "translate(-50%, -50%)", // Center the card
        zIndex: "2000",
        // color: "#062b4d",
        backgroundColor: "rgba(255,255,255)",
        pointerEvents: "auto",
      };
    },
    ...mapState({
      allSimpleElements: (state) => state.allSimpleElements,
      allSimpleSegments: (state) => state.allSimpleSegments,
      defaultShape: (state) => state.defaultShape,
      diagramMode: (state) => state.diagramMode,
      sequences: (state) => state.sequences,
      tutorial: (state) => state.tutorial,
      currentSequenceId: (state) => state.currentSequenceId,
      continuousMode: (state) => state.rifleMode,
      directorsView: (state) => state.immersiveView,
      viewbox: (state) => state.viewbox,
      resetTour: (state) => state.resetTour,
      currentFrame: (state) =>
        state.sequences.get(state.currentSequenceId)?.currentFrame,
    }),
  },
  mounted() {
    // Check if the user has previously dismissed the tour
    const tourDismissed = localStorage.getItem("tourDismissed") === "true";

    if (!tourDismissed) {
      this.updateTargetElement();
    }
  },
  methods: {
    ...mapActions(["updateResetTourAction"]),
    updateTargetElement() {
      const { targetSelector } = this.currentStepInfo;

      if (targetSelector) {
        this.$nextTick(() => {
          this.targetElement = document.querySelector(targetSelector);
        });
      } else {
        this.targetElement = null;
      }
    },
    nextStep() {
      if (this.currentStep < this.tourSteps.length - 1) {
        this.currentStep++;
        this.updateTargetElement();
      } else {
        this.completeTour();
      }
    },
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
        this.updateTargetElement();
      }
    },
    completeTour() {
      if (this.dontShowAgain) {
        localStorage.setItem("tourDismissed", "true");
      }
      this.currentStep = 0;
      this.$emit("updateTourGuide", false);
    },
  },
  watch: {
    currentStep() {
      this.updateTargetElement();
    },
    allSimpleElements(newVal, oldVal) {
      if (this.currentStep === 0 && newVal.size > oldVal.size) {
        this.nextStep();
      } else if (
        this.currentStep === 1 &&
        Array.from(newVal.values()).some((oldElement) =>
          Array.from(oldVal.values()).some(
            (newElement) =>
              newElement.id === oldElement.id &&
              newElement.name !== oldElement.name,
          ),
        )
      ) {
        this.nextStep();
      } else if (
        this.currentStep === 2 &&
        (Array.from(newVal.values()).some((oldElement) =>
          Array.from(oldVal.values()).some(
            (newElement) =>
              newElement.id === oldElement.id &&
              newElement.color !== oldElement.color,
          ),
        ) ||
          Array.from(newVal.values()).some((oldElement) =>
            Array.from(oldVal.values()).some(
              (newElement) =>
                newElement.id === oldElement.id &&
                newElement.textColor !== oldElement.textColor,
            ),
          ) ||
          Array.from(newVal.values()).some((oldElement) =>
            Array.from(oldVal.values()).some(
              (newElement) =>
                newElement.id === oldElement.id &&
                newElement.textFont !== oldElement.textFont,
            ),
          ) ||
          Array.from(newVal.values()).some((oldElement) =>
            Array.from(oldVal.values()).some(
              (newElement) =>
                newElement.id === oldElement.id &&
                newElement.textSize !== oldElement.textSize,
            ),
          ) ||
          Array.from(newVal.values()).some((oldElement) =>
            Array.from(oldVal.values()).some(
              (newElement) =>
                newElement.id === oldElement.id &&
                newElement.textWeight !== oldElement.textWeight,
            ),
          ))
      ) {
        this.nextStep();
      } else if (
        this.currentStep === 3 &&
        newVal.size > oldVal.size &&
        Array.from(newVal.values()).some((element) => element.type !== "SQUARE")
      ) {
        this.nextStep();
      }
    },
    allSimpleSegments(newVal, oldVal) {
      if (this.currentStep === 4 && newVal.size > oldVal.size) {
        this.nextStep();
      }
    },
    resetTour(newVal) {
      if (newVal === true) {
        this.currentStep = 0;
        this.updateTargetElement();
        this.updateResetTourAction(false);
      }
    },
  },
};
</script>

<style scoped>
/* Vuetify-specific overrides */
:deep(.v-card-title) {
  color: #01091a;
}

:deep(.v-card-text) {
  color: #000000;
  padding-top: 16px;
}

:deep(.v-checkbox .v-label) {
  color: #000000;
}

:deep(.gap-2) {
  gap: 8px;
}
</style>
