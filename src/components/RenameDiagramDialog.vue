<template>
  <v-dialog width="512">
    <v-form @submit.prevent="renameDiagram">
      <v-card color="#1c1f22" max-width="400">
        <v-card-title>
          <span class="text-h5">Rename</span>
        </v-card-title>
        <v-card-subtitle>
          <span class="text-h7">This won't save your changes</span>
        </v-card-subtitle>
        <v-card-text>
          <v-text-field
            label="Diagram name"
            append-inner-icon="mdi-check-circle-outline"
            single-line
            hide-details
            :value="diagramName"
            v-model="diagramNameWritable"
            @click:append-inner="renameDiagram"
          ></v-text-field>
        </v-card-text>
      </v-card>
    </v-form>
  </v-dialog>
</template>
<script>
import { mapActions, mapState } from "vuex";
import { setSaveDafault } from "@/files";

export default {
  name: "RenameDiagramDialog",
  methods: {
    ...mapActions(["updateDiagramNameAction"]),
    renameDiagram() {
      this.updateDiagramNameAction(this.creationName);
      setSaveDafault(null); // the name has changed so we need to propose to chose between save methods
      this.$emit("updateRenameDialog", false);
    },
  },
  components: {},
  computed: {
    ...mapState({
      diagramName: (state) => state.diagramName,
    }),
    diagramNameWritable: {
      get() {
        return this.diagramName;
      },
      set(value) {
        this.creationName = value;
      },
    },
  },
  data: function () {
    return {
      creationName: this.diagramName,
    };
  },
};
</script>
