<template>
  <v-dialog
    width="1024"
    height="768"
    scrollable
    persistent
    transition="slide-x-transition"
    no-click-animation
    class="dialog"
  >
    <v-card color="#1c1f22">
      <v-card-title>
        <v-toolbar-title class="app-title">
          <div style="display: flex">
            <img src="/images/logo.svg" alt="SIMULACTION.IO" class="logo-img" />
            <span class="welcome-title">ANIMATE YOUR IDEAS IN SECONDS</span>
          </div>
        </v-toolbar-title>
        <v-toolbar-title
          style="text-align: center; color: #ffffb2; font-size: medium"
        >
          This is an alpha version of Simulaction.<br />
          Please provide feedback using the form link on the top right of the
          app or email <NAME_EMAIL>
        </v-toolbar-title>
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <!-- Gallery Section -->
            <v-col cols="12">
              <h3 style="color: white; text-align: center">
                Explore Demo Projects
              </h3>
              <div class="gallery-grid">
                <div v-for="demo in demos" :key="demo.title" class="demo-item">
                  <div v-if="!demo.loaded" class="spinner"></div>
                  <img
                    :src="demo.thumbnail"
                    @click="loadDemo(demo)"
                    class="demo-thumbnail"
                    :alt="demo.title"
                    @load="demo.loaded = true"
                  />
                  <h4 style="color: white; text-align: center">
                    {{ demo.title }}
                  </h4>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-actions class="card-buttons">
        <v-btn
          v-show="autosavedWorkspace && firstTimeOpening"
          variant="outlined"
          rounded="lg"
          @click="openAutoSaved"
        >
          Resume
        </v-btn>
        <v-btn variant="elevated" rounded="lg" @click="newProject">
          New Project
        </v-btn>
        <v-btn
          variant="elevated"
          rounded="lg"
          color="#5d768d"
          @click="openDiagram"
        >
          Open Project
        </v-btn>
        <v-btn variant="outlined" color="#fff59d" @click="startTutorial"
          >Guided Tutorial
        </v-btn>
        <v-btn
          v-show="!firstTimeOpening"
          variant="outlined"
          color="#ffffff"
          @click="cancel"
          >Cancel
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions, mapState } from "vuex";
import { loadWorkspaceFromDeserialisedContent, resetAll } from "@/core";
import { forceStopAnimation } from "@/animation";
import { v4 as uuidv4 } from "uuid";

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Welcome",
  methods: {
    ...mapActions(["updateDiagramNameAction", "updateDirtyAction", "setDiagramIdAction"]),
    async fetchDemos() {
      try {
        // Fetch the single manifest containing all demo metadata
        const response = await fetch(
          "https://simulaction-welcome-demos.s3-ap-southeast-2.amazonaws.com/manifests/demos.json",
        );
        this.demos = await response.json();
      } catch (error) {
        console.error("Failed to fetch demos:", error);
      }
    },
    loadDemo(demo) {
      fetch(demo.content)
        .then((response) => response.text()) // Fetch the content (URL in `content`)
        .then(async (projectData) => {
          // Load the demo into the workspace
          this.updateDiagramNameAction(demo.title);
          const success = await loadWorkspaceFromDeserialisedContent(
            projectData,
            "Invalid demo, please try another one.",
          );
          if (success) {
            this.$emit("updateWelcomeDialog", false);
            this.firstTimeOpening = false;
          }
        });
    },
    startTutorial() {
      this.$emit("updateTutorialDialog", true);
      this.$emit("updateWelcomeDialog", false);
      this.firstTimeOpening = false;
    },
    openDiagram() {
      this.$emit("choseOpenMethodDialog", true);
      this.$emit("updateWelcomeDialog", false);
      this.firstTimeOpening = false;
    },
    async openAutoSaved() {
      const success = await loadWorkspaceFromDeserialisedContent(
        this.autosavedWorkspace,
        "Unable to open auto-saved project.",
      );
      if (!success) {
        return;
      }
      this.$emit("updateWelcomeDialog", false);
      this.firstTimeOpening = false;
    },
    async newProject() {
      if (this.dirty) {
        const confirm = window.confirm(
          "You have un-saved changes. Do you still want to proceed?",
        );
        if (!confirm) {
          return;
        }
      }
      if (!this.firstTimeOpening) {
        forceStopAnimation();
        await resetAll();
        await this.updateDiagramNameAction("New Diagram");
        await this.updateDirtyAction(false);
      }
      this.setDiagramIdAction(uuidv4());
      this.$emit("updateWelcomeDialog", false);
      this.firstTimeOpening = false;
    },
    cancel() {
      this.$emit("updateWelcomeDialog", false);
      this.firstTimeOpening = false;
    },
  },
  computed: {
    ...mapState({
      diagramName: (state) => state.diagramName,
      dirty: (state) => state.dirty,
    }),
    diagramNameWritable: {
      get() {
        return this.diagramName;
      },
      set(value) {
        this.creationName = value;
      },
    },
  },
  mounted() {
    this.fetchDemos();
    this.demos.forEach((demo) => (demo.loaded = false)); // Initialize loaded state
  },
  data() {
    return {
      demos: [],
      toggleNewBlank: true,
      toggleNewArchitecture: false,
      toggleNewFlowchart: false,
      creationName: "New Diagram",
      firstTimeOpening: true,
      autosavedWorkspace: window.localStorage.getItem("simulaction_autosave"),
    };
  },
};
</script>

<style scoped>
.gallery-grid {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}
.demo-item {
  width: 400px; /* Set a fixed width for the demo item */
  height: 200px; /* Set a fixed height for the demo item */
  text-align: center;
  cursor: pointer;
  padding: 15px 10px;
  position: relative; /* Position relative for spinner */
}
.demo-thumbnail {
  width: 100%;
  height: 100%; /* Ensure the image covers the demo item */
  border-radius: 8px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  object-fit: cover; /* Ensure the image maintains aspect ratio */
}
.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #09f;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.logo-img {
  max-height: 80px; /* Ensure logo scales properly */
  height: auto;
  max-width: 100%; /* Prevent overflow */
  margin-left: 10px;
  margin-top: 10px;
}
.welcome-title {
  margin-left: 30px;
  margin-top: 10px;
  font-family: sans-serif;
  font-size: xx-large;
  font-weight: 600;
  align-content: center;
  text-align: center;
  width: 100%;
}
.dialog {
  background-color: rgba(145, 150, 158, 0.6);
  backdrop-filter: blur(2px);
}
.card-buttons {
  text-align: center;
  display: block;
}
</style>
