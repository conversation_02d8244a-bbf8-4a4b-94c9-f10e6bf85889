<template>
  <v-dialog width="512">
    <v-card color="#1c1f22">
      <v-card-title style="text-align: center">
        <span class="text-h5">Where would you want to save?</span>
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12" md="6">
              <v-btn
                @click="browser"
                height="100px"
                width="100%"
                color="#6d949b"
                ><v-icon size="50">mdi-television</v-icon>
                Browser
              </v-btn>
            </v-col>
            <v-col cols="12" md="6">
              <v-btn
                @click="filesystem"
                height="100px"
                width="100%"
                color="#6d949b"
                ><v-icon size="50">mdi-file-export</v-icon>
                My Files
              </v-btn>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import { saveTypes, setSaveDafault } from "@/files";
import { getDeserializedWorkspaceWithMetadata, saveWorkspaceAs } from "@/core";
import { mapActions } from "vuex";

export default {
  name: "ChoseSaveMethod",
  methods: {
    ...mapActions(["setLatestSnapshotToNotDirtyAction"]),
    browser() {
      let workspace = getDeserializedWorkspaceWithMetadata();
      window.localStorage.setItem(
        workspace.diagramName + ".smlx",
        JSON.stringify(workspace),
      );
      setSaveDafault(saveTypes.browser);
      this.$emit("updateChoseSaveMethodDialog", false);
      this.setLatestSnapshotToNotDirtyAction();
    },
    filesystem() {
      if (saveWorkspaceAs().then()) {
        setSaveDafault(saveTypes.filesystem);
      }
      this.$emit("updateChoseSaveMethodDialog", false);
    },
  },
  components: {},
  computed: {},
  data: function () {
    return {};
  },
};
</script>
<style scoped></style>
