<template>
  <div
    class="timeline-container"
    ref="timelineContainer"
    :style="containerStyle"
  >
    <!-- Dialog for importing sequences -->
    <v-dialog v-model="importSequenceDialog" max-width="500px">
      <v-card>
        <v-card-title>Import Sequence</v-card-title>
        <v-card-text>
          <p>
            Select a sequence to import starting at frame
            {{ Math.floor(currentSequenceFrame) + 1 }}:
          </p>
          <v-select
            v-model="selectedSequenceToImport"
            :items="availableSequencesToImport"
            label="Select Sequence"
            item-title="name"
            item-value="id"
            return-object
          ></v-select>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" text @click="importSequenceDialog = false"
            >Cancel</v-btn
          >
          <v-btn
            color="primary"
            @click="doImportSequence"
            :disabled="!selectedSequenceToImport"
          >
            Import
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <div class="resize-handle" @mousedown="startResize"></div>
    <!-- FIRST ROW: CONTROLS + SPEED -->
    <v-toolbar dense height="36" color="#2a2d32">
      <!-- Playback Controls & Frame -->
      <div class="timeline-header-section" style="width: 370px">
        <v-btn
          icon="mdi-skip-previous"
          @click="decrementFrame"
          :disabled="disableFrameNavigation"
        ></v-btn>
        <v-btn
          :icon="currentSequence?.isPlaying ? 'mdi-pause' : 'mdi-play'"
          @click="toggle"
          id="timelinePlayButton"
        ></v-btn>
        <v-btn icon="mdi-stop" @click="stop"></v-btn>
        <v-btn
          icon="mdi-skip-next"
          @click="incrementFrame"
          :disabled="disableFrameNavigation"
          id="timelineNextButton"
        ></v-btn>
        <v-btn @click="updateRepeat" id="timelineRepeatButton"
          ><v-icon
            v-bind:class="{
              repeatButtonOn: this.repeat,
              repeatButtonOff: !this.repeat,
            }"
          >
            mdi-repeat
          </v-icon></v-btn
        >
        <span class="subheading ml-3"
          >Frame {{ Math.round(currentSequenceFrame * 10) / 10 }}</span
        >
      </div>

      <v-divider vertical class="mx-4"></v-divider>
      <v-tooltip open-delay="600">
        <template v-slot:activator="{ props }">
          <v-btn
            absolute
            icon
            class="camera"
            fab
            color="#d3fdbd"
            v-show="this.diagramMode === 'RECORDING_MODE'"
            @click="setViewAndContinue"
            :disabled="currentSequenceFrame % 1 !== 0 || disableFrameNavigation"
            v-bind="props"
          >
            <v-icon icon="mdi-camera-plus"></v-icon>
          </v-btn>
        </template>
        <span>Set the camera<br />For Director's view</span>
      </v-tooltip>
      <v-tooltip text="Insert Frame" open-delay="600">
        <template v-slot:activator="{ props }">
          <v-btn
            @click="insertFrameCurrentPosition"
            :disabled="currentSequenceFrame % 1 !== 0 || disableFrameNavigation"
            v-bind="props"
            ><v-icon icon="mdi-plus-thick"></v-icon
          ></v-btn>
        </template>
      </v-tooltip>
      <v-tooltip text="Delete Frame" open-delay="600">
        <template v-slot:activator="{ props }">
          <v-btn
            @click="deleteFrameCurrentPosition"
            :disabled="currentSequenceFrame % 1 !== 0 || disableFrameNavigation"
            v-bind="props"
            ><v-icon icon="mdi-minus-thick"></v-icon
          ></v-btn>
        </template>
      </v-tooltip>

      <v-divider vertical class="mx-4"></v-divider>

      <div class="timeline-header-section">
        <v-checkbox
          hide-details="true"
          label="Display Timeline"
          color="#6cffd8"
          density="compact"
          v-model="timelineDisplay"
          style="float: right"
          @change="this.activateAutoSize"
        />
      </div>

      <div class="timeline-header-section">
        <v-checkbox
          hide-details="true"
          label="Manage Sequences"
          color="#6cffd8"
          density="compact"
          v-model="manageSequences"
          style="float: right"
          @change="this.activateAutoSize"
        />
      </div>
      <div class="timeline-header-section">
        <v-checkbox
          hide-details="true"
          label="Auto Size"
          color="yellow"
          density="compact"
          v-model="autoSize"
          @change="handleAutoSizeToggle"
          style="float: right"
          :disabled="autoSize === true"
        />
      </div>
    </v-toolbar>
    <v-toolbar
      dense
      height="36"
      color="#2a2d32"
      style="border: 1px solid #222"
      v-show="manageSequences"
    >
      <!-- Sequence Selection Dropdown -->
      <div class="timeline-header-section" style="width: 10%">
        <v-select
          :items="getSequenceNames"
          :value="sequenceNameCurrentSelection"
          v-model="sequenceNameCurrentSelection"
          v-on:change="sequenceSelect"
          dense
          style="height: 60px"
          hide-details
          :disabled="this.currentSequence?.isPlaying"
        />
      </div>
      <!-- Editing Sequence Name -->
      <div v-if="editingSequenceName" class="timeline-edit-sequence">
        <v-form @submit.prevent="updateSequenceName" dense>
          <v-row align="center" justify="space-between">
            <v-text-field
              v-model="currentSequence.name"
              :value="
                currentSequence ? currentSequence.name : 'No sequence selected'
              "
              dense
              hide-details
              style="flex-grow: 1; width: 200px"
            ></v-text-field>
            <v-btn type="submit" color="primary" style="margin-left: 10px"
              >OK</v-btn
            >
          </v-row>
        </v-form>
      </div>
      <!-- Edit Sequence Name -->
      <v-btn
        icon="mdi-pencil"
        @click="editSequenceName"
        v-if="!editingSequenceName && diagramMode === 'RECORDING_MODE'"
        :disabled="this.currentSequence?.isPlaying"
      ></v-btn>

      <!-- Create New Sequence -->
      <v-btn
        icon="mdi-plus"
        @click="createSequence"
        v-if="!editingSequenceName && diagramMode === 'RECORDING_MODE'"
        :disabled="this.currentSequence?.isPlaying"
      ></v-btn>

      <!-- Import Sequence -->
      <v-btn
        icon="mdi-call-merge"
        @click="showImportSequenceDialog"
        v-if="!editingSequenceName && diagramMode === 'RECORDING_MODE'"
        :disabled="this.currentSequence?.isPlaying"
      ></v-btn>

      <!-- Delete Sequence -->
      <v-btn
        icon="mdi-delete"
        @click="deleteSequence"
        v-if="!editingSequenceName && diagramMode === 'RECORDING_MODE'"
        :disabled="this.currentSequence?.isPlaying"
      ></v-btn>
    </v-toolbar>

    <!-- A relative container wrapping the entire timeline table -->
    <div
      class="timeline-wrapper-container"
      ref="timelineWrapper"
      style="position: relative"
    >
      <div class="timeline-table">
        <!-- Header Row -->
        <div class="timeline-table-row timeline-header-row">
          <div class="timeline-table-cell icon-column">
            <!-- Empty placeholder -->
          </div>
          <div class="timeline-table-cell frame-column">
            <div class="timeline-header">
              <div
                v-for="(frame, index) in totalFrames"
                :key="'frame-header_' + index"
                class="timeline-header-frame"
                ref="timelineHeaderFrame"
                @mousemove="handleTimelineHeaderMousemove($event, index)"
                @mouseleave="hideHoverMarker"
                @click="jumpToFrame(index, $event)"
              >
                <a
                  href="#"
                  :style="{
                    color: 'white',
                    pointerEvents: currentSequence?.isPlaying ? 'none' : 'auto',
                  }"
                  @click.stop.prevent="editFrame(index)"
                >
                  {{ index }}
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- sendData Action Type -->
        <div
          class="timeline-table-row sendData-row"
          v-if="sendDataMax > 0 && timelineDisplay"
        >
          <div class="timeline-table-cell icon-column">
            <v-icon small color="grey">mdi-send</v-icon>
          </div>
          <div class="timeline-table-cell frame-column">
            <template
              v-for="rowIndex in sendDataMax"
              :key="'sendData_' + rowIndex"
            >
              <div class="timeline-row">
                <div
                  v-for="(frame, frameIndex) in totalFrames"
                  :key="'sendDataFrame_' + frameIndex"
                  class="timeline-cell"
                  :data-action-type="'sendData'"
                  :data-frame-index="frameIndex + 1"
                >
                  <template
                    v-for="(row, rowSubIndex) in getActionsForSectionAndFrame(
                      'sendData',
                      frameIndex + 1,
                    ).filter((_, i) => i === rowIndex - 1)"
                    :key="
                      'sendDataRow_' +
                      frameIndex +
                      '_' +
                      rowIndex +
                      '_' +
                      rowSubIndex
                    "
                  >
                    <div
                      v-for="action in row"
                      :key="action.id"
                      class="hoverable"
                      :style="getSendDataStyle(action, frameIndex)"
                      @mousedown="
                        startActionDrag(
                          action,
                          frameIndex + 1,
                          findActionIndexInFrame(action, frameIndex + 1),
                          $event,
                          'sendData',
                        )
                      "
                    >
                      <v-chip
                        :style="
                          getSendDataElementStyle(action.data.elementSource)
                        "
                        small
                        :variant="
                          selectedElementsIds.includes(
                            action.data.elementSource,
                          )
                            ? 'outlined'
                            : 'elevated'
                        "
                        @mouseover="
                          highlightElement(
                            action.data.elementSource,
                            frameIndex,
                          )
                        "
                        @click="
                          selectElement(action.data.elementSource, frameIndex)
                        "
                        @mouseleave="unlightElement(action.data.elementSource)"
                      >
                        {{ getName(action.data.elementSource).charAt(0) }}
                      </v-chip>
                      <v-icon
                        @click="displaySendData(action, frameIndex)"
                        small
                        :style="{ color: action.data.color }"
                        >mdi-play</v-icon
                      >
                      <v-chip
                        :style="
                          getSendDataElementStyle(
                            action.data.elementDestination,
                          )
                        "
                        small
                        :variant="
                          selectedElementsIds.includes(
                            action.data.elementDestination,
                          )
                            ? 'outlined'
                            : 'elevated'
                        "
                        @mouseover="
                          highlightElement(
                            action.data.elementDestination,
                            frameIndex,
                          )
                        "
                        @click="
                          selectElement(
                            action.data.elementDestination,
                            frameIndex,
                          )
                        "
                        @mouseleave="
                          unlightElement(action.data.elementDestination)
                        "
                      >
                        {{ getName(action.data.elementDestination).charAt(0) }}
                      </v-chip>
                      <v-btn
                        icon="mdi-delete"
                        class="delete-icon"
                        @click.stop="deleteAction(action)"
                        density="compact"
                        variant="text"
                        color="red"
                      ></v-btn>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- elementPosition Action Type -->
        <div
          class="timeline-table-row elementPosition-row"
          v-if="elementPositionMax > 0 && timelineDisplay"
        >
          <div class="timeline-table-cell icon-column">
            <v-icon small color="grey">mdi-map-marker</v-icon>
          </div>
          <div class="timeline-table-cell frame-column">
            <template
              v-for="rowIndex in elementPositionMax"
              :key="'elementPosition_' + rowIndex"
            >
              <div class="timeline-row">
                <div
                  v-for="(frame, frameIndex) in totalFrames"
                  :key="'elementPositionFrame_' + frameIndex"
                  class="timeline-cell"
                  :data-action-type="'elementPosition'"
                  :data-frame-index="frameIndex"
                >
                  <template
                    v-for="(row, rowSubIndex) in getActionsForSectionAndFrame(
                      'elementPosition',
                      frameIndex,
                    ).filter((_, i) => i === rowIndex - 1)"
                    :key="
                      'elementPositionRow_' +
                      frameIndex +
                      '_' +
                      rowIndex +
                      '_' +
                      rowSubIndex
                    "
                  >
                    <div
                      v-for="action in row"
                      :key="action.id"
                      class="hoverable"
                      @mousedown="
                        startActionDrag(
                          action,
                          frameIndex,
                          findActionIndexInFrame(action, frameIndex),
                          $event,
                          'elementPosition',
                        )
                      "
                    >
                      <v-chip
                        :style="getElementPositionStyle(action.data.elementId)"
                        :variant="
                          selectedElementsIds.includes(action.data.elementId)
                            ? 'outlined'
                            : 'elevated'
                        "
                        @mouseover="
                          highlightElement(action.data.elementId, frameIndex)
                        "
                        @click="
                          selectElement(action.data.elementId, frameIndex)
                        "
                        @mouseleave="unlightElement(action.data.elementId)"
                      >
                        {{ getName(action.data.elementId).charAt(0) }}
                      </v-chip>
                      <v-btn
                        icon="mdi-delete"
                        class="delete-icon"
                        @click.stop="deleteAction(action)"
                        density="compact"
                        variant="text"
                        color="red"
                      ></v-btn>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- elementOpacity Action Type -->
        <div
          class="timeline-table-row elementOpacity-row"
          v-if="elementOpacityMax > 0 && timelineDisplay"
        >
          <div class="timeline-table-cell icon-column">
            <v-icon small color="grey">mdi-eye-plus-outline</v-icon>
          </div>
          <div class="timeline-table-cell frame-column">
            <template
              v-for="rowIndex in elementOpacityMax"
              :key="'elementOpacity_' + rowIndex"
            >
              <div class="timeline-row">
                <div
                  v-for="(frame, frameIndex) in totalFrames"
                  :key="'elementOpacityFrame_' + frameIndex"
                  class="timeline-cell"
                  :data-action-type="'elementOpacity'"
                  :data-frame-index="frameIndex"
                >
                  <template
                    v-for="(row, rowSubIndex) in getActionsForSectionAndFrame(
                      'elementOpacity',
                      frameIndex,
                    ).filter((_, i) => i === rowIndex - 1)"
                    :key="
                      'elementOpacityRow_' +
                      frameIndex +
                      '_' +
                      rowIndex +
                      '_' +
                      rowSubIndex
                    "
                  >
                    <div
                      v-for="action in row"
                      :key="action.id"
                      class="hoverable"
                      @mousedown="
                        startActionDrag(
                          action,
                          frameIndex,
                          findActionIndexInFrame(action, frameIndex),
                          $event,
                          'elementOpacity',
                        )
                      "
                    >
                      <v-chip
                        :style="getElementPositionStyle(action.data.elementId)"
                        :variant="
                          selectedElementsIds.includes(action.data.elementId)
                            ? 'outlined'
                            : 'elevated'
                        "
                        @mouseover="
                          highlightElement(action.data.elementId, frameIndex)
                        "
                        @click="
                          selectElement(action.data.elementId, frameIndex)
                        "
                        @mouseleave="unlightElement(action.data.elementId)"
                      >
                        {{ getName(action.data.elementId).charAt(0) }}
                      </v-chip>
                      <v-btn
                        icon="mdi-delete"
                        class="delete-icon"
                        @click.stop="deleteAction(action)"
                        density="compact"
                        variant="text"
                        color="red"
                      ></v-btn>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- Imported Sequences Rows -->
        <div
          class="timeline-table-row imported-sequences-row"
          v-if="timelineDisplay && hasImportedSequences"
        >
          <div class="timeline-table-cell icon-column">
            <v-icon small color="grey">mdi-call-merge</v-icon>
          </div>
          <div class="timeline-table-cell frame-column">
            <template
              v-for="rowIndex in importedSequencesMax"
              :key="'importedSequenceRow_' + rowIndex"
            >
              <div class="timeline-row">
                <div
                  v-for="(frame, frameIndex) in totalFrames"
                  :key="'importedSequence_' + rowIndex + '_' + frameIndex"
                  class="timeline-cell"
                  :data-frame-index="frameIndex"
                  :data-row-index="rowIndex - 1"
                >
                  <!-- Handle all marker actions at this frame and row -->
                  <template
                    v-for="markerInfo in getAllImportedSequencesAtFrame(
                      frameIndex,
                    )"
                    :key="markerInfo.action.importData?.importGroupId"
                  >
                    <div
                      v-if="
                        markerInfo.isFirstFrame &&
                        markerInfo.rowIndex === rowIndex - 1
                      "
                      class="imported-sequence-block"
                      :style="{
                        width: `${(markerInfo.action.data.totalFrames - 1) * timelineHeaderFrameWidth}px`,
                      }"
                      @mousedown="
                        startImportedSequenceDrag(
                          $event,
                          frameIndex,
                          markerInfo,
                        )
                      "
                    >
                      {{
                        markerInfo.action.importData
                          ?.currentSourceSequenceName ||
                        markerInfo.action.importData?.sourceSequenceName
                      }}
                      <span
                        class="delete-imported-sequence"
                        @click.stop="deleteImportedSequence(markerInfo)"
                        >&times;</span
                      >
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- viewboxPosition Action Type -->
        <div
          class="timeline-table-row viewbox-row"
          v-if="viewboxMax > 0 && timelineDisplay"
        >
          <div class="timeline-table-cell icon-column">
            <v-icon small color="grey">mdi-video-marker-outline</v-icon>
          </div>
          <div class="timeline-table-cell frame-column">
            <template
              v-for="rowIndex in viewboxMax"
              :key="'viewbox_' + rowIndex"
            >
              <div class="timeline-row">
                <div
                  v-for="(frame, frameIndex) in totalFrames"
                  :key="'viewboxFrame_' + frameIndex"
                  class="timeline-cell"
                  :data-action-type="'viewboxPosition'"
                  :data-frame-index="frameIndex"
                >
                  <template
                    v-for="(row, rowSubIndex) in getActionsForSectionAndFrame(
                      'viewboxPosition',
                      frameIndex,
                    ).filter((_, i) => i === rowIndex - 1)"
                    :key="
                      'viewboxRow_' +
                      frameIndex +
                      '_' +
                      rowIndex +
                      '_' +
                      rowSubIndex
                    "
                  >
                    <div
                      v-for="action in row"
                      :key="action.id"
                      class="hoverable"
                      @mousedown="
                        startActionDrag(
                          action,
                          frameIndex,
                          findActionIndexInFrame(action, frameIndex),
                          $event,
                          'viewboxPosition',
                        )
                      "
                    >
                      <v-icon small color="#d3fdbd">mdi-camera</v-icon>
                      <v-btn
                        icon="mdi-delete"
                        class="delete-icon"
                        @click.stop="deleteAction(action)"
                        density="compact"
                        variant="text"
                        color="red"
                      ></v-btn>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div
        class="timeline-marker"
        :style="markerStyle"
        @mousedown="startMarkerDrag"
        @mouseup="stopMarkerDrag"
      ></div>
      <div
        v-if="hoverMarkerVisible"
        class="timeline-hover-marker"
        :style="{ left: hoverMarkerPosition + 'px' }"
      ></div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from "vuex";
import {
  displaySendData,
  forceStopAnimation,
  pauseSequence,
  playFrame,
  playSequence,
  setupObjectsAtFrame,
  stopSequence,
} from "@/animation";
import {
  createSequence,
  deleteFrame,
  deleteImportedSequence,
  deleteSequence,
  deleteSequenceFrameAction,
  ELEMENT_OPACTITY,
  ELEMENT_POSITION,
  IMPORTED_SEQUENCE,
  importSequence,
  insertFrame,
  moveActionToFrame,
  moveImportedSequence,
  selectSequence,
  SEND_DATA,
  updateViewboxPositionWithLazyLoading,
  VIEWBOX,
} from "@/sequence";
import {
  addElementToSelection,
  deepClone,
  elementIdsAvailableAtFrame,
  highlightElement,
  sleep,
  unlightElement,
} from "@/core";

export default {
  name: "TimelineComponent",
  data() {
    return {
      isDraggingMarker: false,
      markerStartX: 0,
      currentFrameOnDragStart: 0,
      isDraggingTimeline: false,
      startX: 0,
      scrollLeft: 0,
      editingSequenceName: false,
      timelineDisplay: true,
      manageSequences: false,
      timelineHeaderFrameWidth: 100,
      isResizing: false,
      startY: 0,
      startHeight: 0,
      autoSize: true,
      fixedHeight: 90,
      maxHeight: 90, // Maximum height when autoSize is on
      hoverMarkerPosition: null,
      hoverMarkerVisible: false,
      // New properties for action dragging
      isDraggingAction: false,
      draggedAction: null,
      draggedActionType: null,
      draggedActionElement: null,

      // Map to store row indices for imported sequences
      importedSequenceRows: {},
      draggedActionOriginalFrame: null,
      draggedActionIndex: null,
      dragTargetFrame: null,
      dragVisualElement: null,
      // Properties for importing sequences
      importSequenceDialog: false,
      selectedSequenceToImport: null,

      // Properties for imported sequence dragging
      isDraggingImportedSequence: false,
      draggedImportedSequence: null,
      draggedImportedSequenceStartFrame: null,
      dragImportedSequenceTargetFrame: null,
    };
  },
  mounted() {
    // Attach global listeners for drag
    window.addEventListener("mousemove", this.handleGlobalMouseMove);
    window.addEventListener("mouseup", this.handleGlobalMouseUp);
    this.$nextTick(() => {
      this.updateFrameWidth();
    });
  },
  beforeUnmount() {
    window.removeEventListener("mousemove", this.handleGlobalMouseMove);
    window.removeEventListener("mouseup", this.handleGlobalMouseUp);
  },
  computed: {
    ...mapState({
      allElements: (state) => state.allSimpleElements,
      currentSequence: (state) => state.sequences.get(state.currentSequenceId),
      sequences: (state) => state.sequences,
      currentSequenceId: (state) => state.currentSequenceId,
      millisecondsPerProcessingUnit: (state) =>
        state.millisecondsPerProcessingUnit,
      diagramMode: (state) => state.diagramMode,
      selectedElementsIds: (state) => state.selectedElementsIds,
      repeat: (state) => state.repeat,
      immersiveViewState: (state) => state.immersiveView,
    }),
    ...mapGetters([
      "currentWorkspaceSelectedElementsIds",
      "currentWorkspaceSequences",
    ]),
    currentSequenceFrame: {
      get() {
        return this.currentSequence ? this.currentSequence.currentFrame : 0;
      },
      set(value) {
        this.setCurrentFrameForCurrentSequenceAction({
          currentFrame: value,
          sequenceId: this.currentSequenceId,
        });
      },
    },
    totalFrames() {
      return this.currentSequence?.frames.length || 0;
    },
    markerStyle() {
      const iconColumnWidth =
        document.querySelector(".icon-column")?.clientWidth;
      if (!this.currentSequence || this.totalFrames === 0) {
        return { left: `${iconColumnWidth > 0 ? iconColumnWidth : 35}px` };
      }
      const markerPosition =
        this.currentSequence.currentFrame * this.timelineHeaderFrameWidth;
      return {
        left: `${markerPosition + (iconColumnWidth > 0 ? iconColumnWidth : 35)}px`,
        transition:
          this.isDraggingMarker || this.currentSequence.currentFrame === 0
            ? "none"
            : this.currentSequence?.isPlaying
              ? `left 0.5s linear`
              : "left 0.15s linear",
      };
    },

    sequenceNameCurrentSelection: {
      get() {
        return this.currentSequence ? this.currentSequence.name : null;
      },
      set(value) {
        const seq = Array.from(this.sequences.values()).find(
          (sequence) => sequence.name === value,
        );
        if (seq) {
          this.sequenceSelect(seq.id);
        }
      },
    },
    disableFrameNavigation() {
      return (
        !this.currentSequence ||
        (this.currentSequence.isPlaying && !this.currentSequence.isPaused)
      );
    },
    sendDataMax() {
      return this.getNumberOfRowsForSection("sendData");
    },
    elementPositionMax() {
      return this.getNumberOfRowsForSection("elementPosition");
    },
    elementOpacityMax() {
      return this.getNumberOfRowsForSection("elementOpacity");
    },
    viewboxMax() {
      return this.getNumberOfRowsForSection("viewboxPosition");
    },
    importedSequencesMax() {
      if (!this.currentSequence || !this.importedSequenceRows) return 1;

      // Get the maximum row index assigned to any imported sequence
      const rowIndices = Object.values(this.importedSequenceRows);
      if (rowIndices.length === 0) return 1;

      // Add 1 because row indices are 0-based
      return Math.max(...rowIndices) + 1;
    },
    getSequenceNames() {
      return this.currentWorkspaceSequences.map((sequence) => sequence.name);
    },
    containerStyle() {
      // If autoSize is enabled, let the height grow naturally
      // Otherwise, use the fixed height
      return this.autoSize
        ? { height: "auto" }
        : { height: `${this.fixedHeight}px` };
    },
    // Removed isAtOrAfterLastFrame computed property as it's no longer needed
    isAtOrAfterLastFrame() {
      // Always return true to allow importing at any frame
      return true;
    },
    availableSequencesToImport() {
      if (!this.currentSequence) return [];
      // Convert sequences Map to array and filter out the current sequence
      // and sequences from other workspaces
      return Array.from(this.sequences.values()).filter(
        (seq) => seq.id !== this.currentSequenceId,
      );
    },
    hasImportedSequences() {
      if (!this.currentSequence || !this.currentSequence.frames) return false;

      // Check if any frame has an IMPORTED_SEQUENCE action
      for (const frame of this.currentSequence.frames) {
        if (
          frame &&
          frame.some((action) => action.metadata.type === IMPORTED_SEQUENCE)
        ) {
          return true;
        }
      }

      return false;
    },
  },
  methods: {
    insertFrame,
    ...mapActions([
      "setCurrentFrameForCurrentSequenceAction",
      "updateSpeedAction",
      "updateSequenceAction",
      "showRightDrawerAction",
      "updateRightDrawerHeaderTabAction",
      "updateRightDrawerSequencesSubTabAction",
      "updateRepeatAction",
      "toggleImmersiveViewAction",
    ]),
    updateFrameWidth() {
      const timelineHeaderFrame = this.$refs.timelineHeaderFrame?.at(0);
      if (timelineHeaderFrame) {
        this.timelineHeaderFrameWidth = timelineHeaderFrame.clientWidth || 0;
      }
    },
    sequenceSelect(sequenceId) {
      selectSequence(sequenceId);

      // Reset the row assignments when selecting a different sequence
      this.importedSequenceRows = {};

      setupObjectsAtFrame(
        sequenceId,
        this.sequences.get(this.currentSequenceId).currentFrame,
      );
    },
    startMarkerDrag(e) {
      e.stopPropagation();
      this.isDraggingMarker = true;

      // Record initial conditions
      this.markerStartX = e.pageX;
      this.currentFrameOnDragStart = this.currentSequenceFrame;
    },

    stopMarkerDrag() {
      this.currentSequenceFrame =
        Math.round(this.currentSequenceFrame * 10) / 10;
    },

    handleGlobalMouseMove(e) {
      if (this.isDraggingMarker) {
        this.dragMarker(e);
      } else if (this.isDragging) {
        // If we are dragging timeline (if you keep that feature)
        this.handleTimelineScroll(e);
      } else if (this.isDraggingAction) {
        // Handle action dragging motion
        this.handleActionDragMove(e);
      }
    },

    handleGlobalMouseUp(e) {
      this.isDraggingMarker = false;
      this.isDragging = false; // Also stop timeline dragging if that was ongoing

      // Handle action dragging completion
      if (this.isDraggingAction) {
        this.finishActionDrag(e);
      }
    },

    dragMarker(e) {
      if (!this.currentSequence || this.totalFrames === 0) return;

      const deltaX = e.pageX - this.markerStartX;

      const deltaFrames = deltaX / this.timelineHeaderFrameWidth;

      // Instead of flooring, just use the fractional value
      let newFrame = this.currentFrameOnDragStart + deltaFrames;

      // Clamp newFrame to valid range, but do not round it
      if (newFrame < 0) newFrame = 0;
      if (newFrame > this.totalFrames - 1) newFrame = this.totalFrames - 1;

      this.currentSequenceFrame = newFrame;
      forceStopAnimation();
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
      );
    },
    handleTimelineScroll(e) {
      if (!this.isDragging) return;
      const x = e.pageX - this.$refs.timelineWrapper.offsetLeft;
      const walk = x - this.startX;
      this.$refs.timelineWrapper.scrollLeft = this.scrollLeft - walk;
    },
    getActionsForFrame(index) {
      return this.currentSequence?.frames[index] || [];
    },
    getActionsForSectionAndFrame(section, frameIndex) {
      const actions = this.getActionsForFrame(frameIndex);
      const sectionActions = actions.filter(
        (action) =>
          action.metadata.type === section &&
          !action.metadata.interpolated &&
          !(frameIndex === 0 && action.metadata.type === "elementOpacity"),
      );
      const rows = [];
      sectionActions.forEach((action) => {
        rows.push([action]);
      });
      return rows;
    },

    // Method to get all imported sequences at a specific frame
    getAllImportedSequencesAtFrame(frameIndex) {
      if (!this.currentSequence) return [];

      // Get all marker actions at this frame
      const frame = this.currentSequence.frames[frameIndex] || [];
      const markerActions = frame.filter(
        (action) => action.metadata.type === IMPORTED_SEQUENCE,
      );

      // Sort marker actions alphabetically by sequence name
      markerActions.sort((a, b) => {
        const nameA =
          a.importData?.currentSourceSequenceName ||
          a.importData?.sourceSequenceName ||
          "";
        const nameB =
          b.importData?.currentSourceSequenceName ||
          b.importData?.sourceSequenceName ||
          "";
        return nameA.localeCompare(nameB);
      });

      // If we have marker actions, process them
      const result = [];

      // Process each marker action
      for (const markerAction of markerActions) {
        // Calculate the end frame based on visual frames
        const visualFrames = markerAction.data.visualFrames || 0;
        const endFrame = frameIndex + visualFrames;

        // Get the row index for this sequence
        const rowIndex = this.getRowIndexForImportedSequence(
          markerAction,
          frameIndex,
          endFrame,
        );

        result.push({
          isPartOf: true,
          action: markerAction,
          startFrame: frameIndex,
          endFrame: endFrame,
          isFirstFrame: true,
          rowIndex: rowIndex,
        });
      }

      // Also check for imported actions (non-marker actions)
      const importedActions = frame.filter((action) => action.importData);

      // Sort imported actions by their import group ID for consistency
      importedActions.sort((a, b) => {
        const groupIdA = a.importData?.importGroupId || "";
        const groupIdB = b.importData?.importGroupId || "";
        return groupIdA.localeCompare(groupIdB);
      });

      // Process each imported action
      for (const importedAction of importedActions) {
        const importGroupId = importedAction.importData?.importGroupId;

        // Skip if we already have a marker action for this import group
        if (
          result.some(
            (r) => r.action.importData?.importGroupId === importGroupId,
          )
        ) {
          continue;
        }

        // Find the marker action for this import group
        for (let i = 0; i < this.currentSequence.frames.length; i++) {
          const checkFrame = this.currentSequence.frames[i] || [];
          const markerAction = checkFrame.find(
            (action) =>
              action.metadata.type === IMPORTED_SEQUENCE &&
              action.importData?.importGroupId === importGroupId,
          );

          if (markerAction) {
            // Calculate the end frame based on visual frames
            const visualFrames = markerAction.data.visualFrames || 0;
            const displayStartFrame = i - 1;
            const displayEndFrame = displayStartFrame + visualFrames;

            // Get the row index for this sequence
            const rowIndex = this.getRowIndexForImportedSequence(
              markerAction,
              displayStartFrame,
              displayEndFrame,
            );

            result.push({
              isPartOf: true,
              action: markerAction,
              startFrame: displayStartFrame,
              endFrame: displayEndFrame,
              isFirstFrame: false,
              rowIndex: rowIndex,
            });

            break;
          }
        }
      }

      return result;
    },

    // Method to check if a frame is part of an imported sequence (legacy method for compatibility)
    isFramePartOfImportedSequence(frameIndex) {
      if (!this.currentSequence) return { isPartOf: false };

      // For timeline display, we need to check the frame after the current one
      // This accounts for the offset between the sequence data and timeline display
      const dataFrameIndex = frameIndex + 1;

      // Check if the data frame has an imported sequence marker action
      const frame = this.currentSequence.frames[dataFrameIndex] || [];

      // Find all marker actions in this frame (there could be multiple at the same frame)
      const markerActions = frame.filter(
        (action) => action.metadata.type === IMPORTED_SEQUENCE,
      );

      // Get the first marker action (traditional approach)
      const markerAction = markerActions[0];

      // Check if the data frame has any actions with importData
      const importedAction = frame.find((action) => action.importData);

      // If we have a marker action, use that for display
      if (markerAction) {
        // Calculate the end frame based on visual frames
        const visualFrames = markerAction.data.visualFrames || 0;
        // We're already using the correct frame index for display
        const endFrame = frameIndex + visualFrames;

        // Get the row index for this sequence (if not already assigned)
        const rowIndex = this.getRowIndexForImportedSequence(
          markerAction,
          frameIndex,
          endFrame,
        );

        return {
          isPartOf: true,
          action: markerAction,
          startFrame: frameIndex,
          endFrame: endFrame,
          isFirstFrame: true,
          rowIndex: rowIndex,
        };
      }
      // If we have an imported action but no marker, this is part of an imported sequence
      // but not the first frame
      else if (importedAction) {
        const importGroupId = importedAction.importData?.importGroupId;

        // Find the first frame with a marker action for this import group
        for (let i = 0; i < this.currentSequence.frames.length; i++) {
          const checkFrame = this.currentSequence.frames[i] || [];
          const markerAction = checkFrame.find(
            (action) =>
              action.metadata.type === IMPORTED_SEQUENCE &&
              action.importData?.importGroupId === importGroupId,
          );

          if (markerAction) {
            // Calculate the end frame based on visual frames
            const visualFrames = markerAction.data.visualFrames || 0;
            // Convert from data frame index to display frame index
            const displayStartFrame = i - 1;
            const displayEndFrame = displayStartFrame + visualFrames;

            // Get the row index for this sequence (if not already assigned)
            const rowIndex = this.getRowIndexForImportedSequence(
              markerAction,
              displayStartFrame,
              displayEndFrame,
            );

            return {
              isPartOf: true,
              action: markerAction,
              startFrame: displayStartFrame,
              endFrame: displayEndFrame,
              isFirstFrame: false,
              rowIndex: rowIndex,
            };
          }
        }
      }

      return { isPartOf: false };
    },

    // Helper method to get or assign a row index for an imported sequence
    getRowIndexForImportedSequence(markerAction, startFrame, endFrame) {
      // Use a unique ID for the sequence
      const sequenceId = markerAction.importData?.importGroupId;

      // If we already have a row assigned for this sequence, return it
      if (
        this.importedSequenceRows &&
        this.importedSequenceRows[sequenceId] !== undefined
      ) {
        return this.importedSequenceRows[sequenceId];
      }

      // Initialize the rows map if it doesn't exist
      if (!this.importedSequenceRows) {
        this.importedSequenceRows = {};
      }

      // Get all imported sequences in the timeline for consistent ordering
      const allSequences = [];

      // Iterate through all frames to find marker actions
      for (let i = 0; i < this.currentSequence.frames.length; i++) {
        const frame = this.currentSequence.frames[i] || [];
        const markers = frame.filter(
          (action) => action.metadata.type === IMPORTED_SEQUENCE,
        );

        for (const marker of markers) {
          // Calculate the display frame indices
          const displayStartFrame = i - 1;
          const displayEndFrame =
            displayStartFrame + (marker.data.visualFrames || 0);

          // Check if we already have this sequence in our list
          const existingIndex = allSequences.findIndex(
            (seq) => seq.id === marker.importData?.importGroupId,
          );
          if (existingIndex === -1) {
            // Add this sequence to our list
            allSequences.push({
              id: marker.importData?.importGroupId,
              name:
                marker.importData?.currentSourceSequenceName ||
                marker.importData?.sourceSequenceName ||
                "",
              startFrame: displayStartFrame,
              endFrame: displayEndFrame,
              rowIndex:
                this.importedSequenceRows[marker.importData?.importGroupId] ||
                0,
            });
          }
        }
      }

      // Sort sequences by ID for consistent ordering
      allSequences.sort((a, b) => a.id.localeCompare(b.id));

      // Find the current sequence in the sorted list
      const currentSequenceIndex = allSequences.findIndex(
        (seq) => seq.id === sequenceId,
      );

      // If the sequence isn't in the list yet (new sequence), add it
      if (currentSequenceIndex === -1) {
        allSequences.push({
          id: sequenceId,
          name:
            markerAction.importData?.currentSourceSequenceName ||
            markerAction.importData?.sourceSequenceName ||
            "",
          startFrame: startFrame,
          endFrame: endFrame,
          rowIndex: 0,
        });
        // Re-sort the list
        allSequences.sort((a, b) => a.id.localeCompare(b.id));
      }

      // Get the sequences that overlap with the current sequence
      const overlappingSequences = allSequences.filter(
        (seq) =>
          seq.id !== sequenceId && // Not the current sequence
          !(startFrame === seq.endFrame || seq.startFrame === endFrame) && // Not exactly touching
          startFrame < seq.endFrame &&
          endFrame > seq.startFrame, // Overlapping in time
      );

      // If there are no overlapping sequences, use row 0
      if (overlappingSequences.length === 0) {
        this.importedSequenceRows[sequenceId] = 0;
        return 0;
      }

      // Find the first available row
      let rowIndex = 0;
      let foundRow = false;

      while (!foundRow) {
        foundRow = true;

        // Check if this row is used by any overlapping sequence
        for (const seq of overlappingSequences) {
          if (seq.rowIndex === rowIndex) {
            // This row is already used, try the next one
            foundRow = false;
            rowIndex++;
            break;
          }
        }
      }

      // Assign and store the row index
      this.importedSequenceRows[sequenceId] = rowIndex;

      return rowIndex;
    },
    getNumberOfRowsForSection(section) {
      let maxNumberOfRows = 0;
      this.currentSequence?.frames.forEach((frame, index) => {
        const count = frame.filter(
          (action) =>
            action.metadata.type === section &&
            !action.metadata.interpolated &&
            !(index === 0 && action.metadata.type === "elementOpacity"),
        ).length;
        if (count > maxNumberOfRows) {
          maxNumberOfRows = count;
        }
      });
      return maxNumberOfRows;
    },

    getSendDataStyle() {
      return {
        width: `${this.timelineHeaderFrameWidth}px`,
        border: "1px solid grey",
      };
    },

    getSendDataElementStyle(elementId) {
      return {
        width: `${this.timelineHeaderFrameWidth / 3}px`,
        color: this.allElements.get(elementId)?.color || "grey",
        height: "80%",
      };
    },

    getElementPositionStyle(elementId) {
      return {
        width: "40%",
        color: this.allElements.get(elementId)?.textColor || "grey",
        background: this.allElements.get(elementId)?.color,
        height: "80%",
        marginLeft: "-2px",
      };
    },

    getName(elementId) {
      return this.allElements.get(elementId)?.name || "...";
    },

    toggle() {
      if (this.currentSequence.isPlaying) {
        pauseSequence(this.currentSequenceId);
      } else {
        playSequence(this.currentSequenceId);
      }
    },
    stop() {
      stopSequence(this.currentSequenceId);
    },
    async incrementFrame() {
      let currentSpeed = this.millisecondsPerProcessingUnit;
      let frameTicksElapsed =
        Math.round((this.currentSequence.currentFrame % 1) * 10) / 10;
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: 500,
        undoable: false,
      });
      this.currentSequence.isPlaying = false;
      forceStopAnimation();

      const ranAction = playFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame + 1,
        null,
        frameTicksElapsed,
        true,
      );
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: currentSpeed,
        undoable: false,
      });
      this.currentSequence.currentFrame = Math.floor(
        this.currentSequence.currentFrame + 1,
      );
      if (ranAction) {
        await sleep(500);
      }
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
        true,
      );
    },
    async decrementFrame() {
      if (this.currentSequence.currentFrame <= 0) {
        return;
      }
      let currentSpeed = this.millisecondsPerProcessingUnit;
      let frameTicksElapsed =
        Math.round((this.currentSequence.currentFrame % 1) * 10) / 10;
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: 500,
        undoable: false,
      });
      if (frameTicksElapsed === 0) {
        this.currentSequence.currentFrame--;
      } else {
        this.currentSequence.currentFrame = Math.floor(
          this.currentSequence.currentFrame,
        );
      }
      this.currentSequence.isPlaying = false;
      forceStopAnimation();
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
        true,
      );
      playFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame + 1,
        true,
        frameTicksElapsed === 0 ? 1 : frameTicksElapsed,
        true,
      );
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: currentSpeed,
        undoable: false,
      });
    },
    updateRepeat() {
      this.updateRepeatAction(!this.repeat);
    },
    async setViewAndContinue() {
      if (!this.sequences || this.sequences.size === 0) {
        await createSequence({});
      }
      await updateViewboxPositionWithLazyLoading({
        sequence: this.sequences.get(this.currentSequenceId),
        frameIndex: this.sequences.get(this.currentSequenceId).currentFrame,
        elementPosition: null,
        undoable: false,
      });
      let directorViewIsOn = this.immersiveViewState;
      if (!directorViewIsOn) {
        this.toggleImmersiveViewAction(true);
      }
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.sequences.get(this.currentSequenceId).currentFrame,
      );
    },

    async insertFrameCurrentPosition() {
      await insertFrame(
        deepClone(this.sequences.get(this.currentSequenceId)),
        this.currentSequence.currentFrame,
      );
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
      );
    },

    async deleteFrameCurrentPosition() {
      const sendDataActions = this.currentSequence.frames[
        this.currentSequence.currentFrame + 1
      ]?.filter((action) => action.metadata.type === SEND_DATA);
      const elementActions = this.currentSequence.frames[
        this.currentSequence.currentFrame
      ]?.filter(
        (action) =>
          !action.metadata.interpolated &&
          (action.metadata.type === ELEMENT_OPACTITY ||
            action.metadata.type === ELEMENT_POSITION),
      );
      const viewAction = this.currentSequence.frames[
        this.currentSequence.currentFrame
      ]?.filter(
        (action) =>
          action.metadata.type === VIEWBOX && !action.metadata.interpolated,
      );
      const numberOfActionsCurrentFrame =
        sendDataActions?.length + elementActions?.length + viewAction?.length;
      if (numberOfActionsCurrentFrame > 0) {
        const confirm = window.confirm(
          "There " +
            (numberOfActionsCurrentFrame > 1 ? "are " : "is ") +
            numberOfActionsCurrentFrame +
            " action" +
            (numberOfActionsCurrentFrame > 1 ? "s" : "") +
            " created in this frame. " +
            (numberOfActionsCurrentFrame > 1 ? "They " : "It ") +
            "will be deleted. Do you still want to proceed?",
        );
        if (!confirm) {
          return;
        }
      }
      await deleteFrame(
        deepClone(this.currentSequence),
        this.currentSequence.currentFrame,
      );
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequence.currentFrame,
      );
    },

    async deleteAction(action) {
      // Find the frame that contains this action
      const frames = this.currentSequence.frames;
      let frameIndex = -1;
      let actionIndex = -1;

      for (let i = 0; i < frames.length; i++) {
        const idx = frames[i].indexOf(action);
        if (idx !== -1) {
          frameIndex = i;
          actionIndex = idx;
          break;
        }
      }
      await deleteSequenceFrameAction({
        sequence: deepClone(this.sequences.get(this.currentSequenceId)),
        frameIndex,
        actionIndex,
        undoable: true,
      });
      setupObjectsAtFrame(
        this.currentSequence.id,
        this.currentSequence.currentFrame,
      );
    },
    updateSequenceName() {
      this.editingSequenceName = false;
      const sequence = this.currentSequence;
      this.updateSequenceAction({
        id: sequence.id,
        sequence: sequence,
        undoable: true,
      });
    },
    editSequenceName() {
      this.editingSequenceName = true;
    },
    async createSequence() {
      await createSequence({ undoable: false });
    },
    deleteSequence() {
      deleteSequence({
        sequenceId: this.currentSequenceId,
        undoable: false,
        recreateDefault: true,
      });
    },
    activateAutoSize() {
      this.autoSize = true;
      this.handleAutoSizeToggle();
    },
    updateMaxHeight() {
      const timelineContainer = this.$refs.timelineContainer;
      this.maxHeight = timelineContainer.scrollHeight; // Set maxHeight to content height
    },

    // Methods for importing sequences
    showImportSequenceDialog() {
      // No longer checking if we're at or after the last frame
      this.selectedSequenceToImport = null;
      this.importSequenceDialog = true;
    },

    async doImportSequence() {
      if (!this.selectedSequenceToImport || !this.currentSequence) {
        return;
      }

      const result = await importSequence(
        this.currentSequence,
        this.selectedSequenceToImport,
      );

      if (result.success) {
        this.importSequenceDialog = false;

        // Reset the row assignments to recalculate them based on new positions
        this.importedSequenceRows = {};

        // Wait a moment for the store to update
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Refresh the view
        setupObjectsAtFrame(
          this.currentSequenceId,
          this.currentSequence.currentFrame,
        );
      } else {
        alert(result.message);
      }
    },

    // Methods for handling imported sequences
    startImportedSequenceDrag(event, frameIndex, sequenceInfo) {
      // Prevent default to avoid text selection
      event.preventDefault();

      this.isDraggingImportedSequence = true;
      this.draggedImportedSequence = sequenceInfo;
      this.draggedImportedSequenceStartFrame = frameIndex;
      this.dragStartX = event.clientX;

      // Add event listeners for drag
      window.addEventListener("mousemove", this.handleImportedSequenceDrag);
      window.addEventListener("mouseup", this.stopImportedSequenceDrag);
    },

    handleImportedSequenceDrag(event) {
      if (!this.isDraggingImportedSequence) return;

      // Calculate frame offset based on mouse movement
      const dx = event.clientX - this.dragStartX;
      const frameOffset = Math.round(dx / this.timelineHeaderFrameWidth);

      // Update the target frame
      this.dragImportedSequenceTargetFrame =
        this.draggedImportedSequenceStartFrame + frameOffset;

      // Highlight the target position (or remove highlight if back at start position)
      this.highlightImportedSequenceTargetPosition(
        this.dragImportedSequenceTargetFrame,
      );
    },

    highlightImportedSequenceTargetPosition(targetFrame) {
      // Remove previous highlights
      document.querySelectorAll(".imported-sequence-target").forEach((el) => {
        el.classList.remove("imported-sequence-target");
      });

      // Add highlight to target frames only if the target frame is different from the start frame
      if (
        targetFrame >= 0 &&
        targetFrame !== this.draggedImportedSequenceStartFrame
      ) {
        // Calculate the number of frames to highlight (should match the actual sequence length)
        const framesToHighlight =
          this.draggedImportedSequence.action.data.totalFrames - 2; // Subtract 2 to account for inclusive loop

        // For frame 0, we need to ensure we don't highlight negative frames
        const startHighlight = Math.max(0, targetFrame);
        const endHighlight = targetFrame + framesToHighlight;

        // Get the row index for the dragged sequence
        const rowIndex = this.draggedImportedSequence.rowIndex;

        for (let i = startHighlight; i <= endHighlight; i++) {
          // Specifically target cells in the imported sequences row with the correct row index
          const frameEl = document.querySelector(
            `.imported-sequences-row .timeline-cell[data-frame-index="${i}"][data-row-index="${rowIndex}"]`,
          );
          if (frameEl) {
            frameEl.classList.add("imported-sequence-target");
          }
        }
      }
    },

    async stopImportedSequenceDrag(event) {
      if (!this.isDraggingImportedSequence) return;

      // Remove event listeners
      window.removeEventListener("mousemove", this.handleImportedSequenceDrag);
      window.removeEventListener("mouseup", this.stopImportedSequenceDrag);

      // Remove highlights
      document.querySelectorAll(".imported-sequence-target").forEach((el) => {
        el.classList.remove("imported-sequence-target");
      });

      // Calculate the actual frame offset based on mouse movement
      const dx = event.clientX - this.dragStartX;
      const frameOffset = Math.round(dx / this.timelineHeaderFrameWidth);

      // Move the imported sequence if needed
      if (
        this.dragImportedSequenceTargetFrame !== null &&
        frameOffset !== 0 // Only move if there's a significant movement
      ) {
        await this.moveImportedSequence(
          this.draggedImportedSequence,
          this.dragImportedSequenceTargetFrame,
        );
      }

      // Reset drag state
      this.isDraggingImportedSequence = false;
      this.draggedImportedSequence = null;
      this.draggedImportedSequenceStartFrame = null;
      this.dragImportedSequenceTargetFrame = null;
    },

    async moveImportedSequence(sequenceInfo, targetFrame) {
      if (!this.currentSequence) return;

      // Call the moveImportedSequence function from sequence.js
      const result = await moveImportedSequence(
        this.currentSequence,
        sequenceInfo.action.importData?.importGroupId,
        sequenceInfo.startFrame,
        targetFrame,
      );

      if (result.success) {
        // Reset the row assignments to recalculate them based on new positions
        this.importedSequenceRows = {};

        // Wait a moment for the store to update
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Refresh the view
        setupObjectsAtFrame(
          this.currentSequenceId,
          this.currentSequence.currentFrame,
        );
      } else {
        alert(result.message);
      }
    },

    async deleteImportedSequence(sequenceInfo) {
      if (!this.currentSequence) return;

      // Confirm deletion
      const sequenceName =
        sequenceInfo.action.importData?.currentSourceSequenceName ||
        sequenceInfo.action.importData?.sourceSequenceName;
      if (
        !confirm(
          `Are you sure you want to delete the imported sequence '${sequenceName}'?`,
        )
      ) {
        return;
      }

      // Use the deleteImportedSequence function from sequence.js
      const result = await deleteImportedSequence({
        sequence: this.currentSequence,
        importGroupId: sequenceInfo.action.importData?.importGroupId,
      });

      if (result.success) {
        // Reset the row assignments to recalculate them based on new positions
        this.importedSequenceRows = {};

        // Wait a moment for the store to update
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Refresh the view
        setupObjectsAtFrame(
          this.currentSequenceId,
          this.currentSequence.currentFrame,
        );
      } else {
        alert(result.message);
      }
    },
    startResize(event) {
      if (this.autoSize) {
        this.autoSize = false; // Disable autoSize on manual resize
        this.updateMaxHeight(); // Capture the maxHeight when disabling autoSize
      }

      this.isResizing = true;
      this.startY = event.clientY;
      this.startHeight = this.fixedHeight;
      window.addEventListener("mousemove", this.handleResize);
      window.addEventListener("mouseup", this.stopResize);
    },
    handleResize(event) {
      if (!this.isResizing) return;

      const deltaY = event.clientY - this.startY;
      let newHeight = this.startHeight - deltaY;

      // Restrict height between 90px and maxHeight
      newHeight = Math.min(Math.max(newHeight, 90), this.maxHeight);

      this.fixedHeight = newHeight; // Update fixed height
      this.$refs.timelineContainer.style.height = `${newHeight}px`;
    },
    stopResize() {
      this.isResizing = false;

      // Remove global listeners
      window.removeEventListener("mousemove", this.handleResize);
      window.removeEventListener("mouseup", this.stopResize);
    },
    handleAutoSizeToggle() {
      if (this.autoSize) {
        // Reset the height to natural content height when enabling autoSize
        this.fixedHeight = this.$refs.timelineContainer.offsetHeight;
        this.updateMaxHeight(); // Recalculate maxHeight
      }
    },
    editFrame(index) {
      this.setCurrentFrameForCurrentSequenceAction({
        sequenceId: this.currentSequence.id,
        currentFrame: index,
      });
      setupObjectsAtFrame(this.currentSequence.id, index, true);
      this.updateRightDrawerHeaderTabAction("sequences");
      this.updateRightDrawerSequencesSubTabAction("frame");
      this.showRightDrawerAction(true);
    },
    highlightElement(elementId, frameIndex) {
      if (
        elementIdsAvailableAtFrame(this.currentSequenceId, frameIndex).includes(
          elementId,
        )
      ) {
        return;
      }
      highlightElement(elementId);
    },
    unlightElement(elementId) {
      if (!this.selectedElementsIds.includes(elementId)) {
        unlightElement(elementId);
      }
    },
    selectElement(elementId, frameIndex) {
      if (
        elementIdsAvailableAtFrame(this.currentSequenceId, frameIndex).includes(
          elementId,
        )
      ) {
        return;
      }
      addElementToSelection({
        elementId,
        deselectOthers: true,
        undoable: false,
      });
    },
    displaySendData(action, frameIndex) {
      let currentSpeed = this.millisecondsPerProcessingUnit;
      if (
        !elementIdsAvailableAtFrame(
          this.currentSequenceId,
          frameIndex,
        ).includes(action.data.elementSource) &&
        !elementIdsAvailableAtFrame(
          this.currentSequenceId,
          frameIndex,
        ).includes(action.data.elementDestination)
      ) {
        return;
      }

      this.updateSpeedAction({
        millisecondsPerProcessingUnit: 800,
        undoable: false,
      });
      displaySendData(
        action.data.elementSource,
        action.data.elementDestination,
        action.data.color,
      );
      this.updateSpeedAction({
        millisecondsPerProcessingUnit: currentSpeed,
        undoable: false,
      });
    },
    handleTimelineHeaderMousemove(event) {
      const wrapperRect = this.$refs.timelineWrapper.getBoundingClientRect();

      // mouse position relative to timeline wrapper container
      const cursorX =
        event.clientX -
        wrapperRect.left +
        this.$refs.timelineWrapper.scrollLeft;

      // Set the hover marker exactly under the cursor
      this.hoverMarkerPosition = cursorX;
      this.hoverMarkerVisible = true;
    },

    hideHoverMarker() {
      this.hoverMarkerVisible = false;
    },

    jumpToFrame(frameIndex, event) {
      const frameRect = event.currentTarget.getBoundingClientRect();
      const relativeX = event.clientX - frameRect.left;
      const fraction = relativeX / frameRect.width;

      this.currentSequenceFrame = parseFloat(
        (frameIndex + fraction).toFixed(1),
      );
      forceStopAnimation();
      setupObjectsAtFrame(
        this.currentSequenceId,
        this.currentSequenceFrame,
        true,
      );
    },
    // New methods for action dragging
    startActionDrag(action, frameIndex, actionIndex, event, actionType) {
      // Prevent default to stop browser default drag behavior
      event.preventDefault();

      if (actionIndex === -1) {
        console.warn(
          "Could not find action index in frame. Drag operation may fail.",
        );
      }

      // Store the action and its original position
      this.isDraggingAction = true;
      this.draggedAction = action;
      this.draggedActionType = actionType;
      this.draggedActionOriginalFrame = frameIndex;
      this.draggedActionIndex = actionIndex;

      // Create a visual element for dragging
      const dragVisual = document.createElement("div");
      dragVisual.className = "dragging-action-visual";

      // Style based on action type
      if (actionType === "sendData") {
        dragVisual.innerHTML = `
          <span>${this.getName(action.data.elementSource).charAt(0)}</span>
          <span>→</span>
          <span>${this.getName(action.data.elementDestination).charAt(0)}</span>
        `;
        dragVisual.style.color = action.data.color || "white";
      } else if (
        actionType === "elementPosition" ||
        actionType === "elementOpacity"
      ) {
        dragVisual.innerHTML = `<span>${this.getName(action.data.elementId).charAt(0)}</span>`;
        dragVisual.style.background =
          this.allElements.get(action.data.elementId)?.color || "grey";
      } else if (actionType === "viewboxPosition") {
        dragVisual.innerHTML = `<span>📷</span>`;
        dragVisual.style.color = "#d3fdbd";
      }

      // Position it at the cursor
      dragVisual.style.position = "fixed";
      dragVisual.style.zIndex = "9999";
      dragVisual.style.opacity = "0.8";
      dragVisual.style.pointerEvents = "none";
      dragVisual.style.padding = "8px";
      dragVisual.style.borderRadius = "4px";
      dragVisual.style.backgroundColor = "rgba(50, 50, 50, 0.8)";
      dragVisual.style.boxShadow = "0 2px 8px rgba(0, 0, 0, 0.3)";
      dragVisual.style.left = `${event.clientX}px`;
      dragVisual.style.top = `${event.clientY}px`;
      dragVisual.style.transform = "translate(-50%, -50%)";

      document.body.appendChild(dragVisual);
      this.dragVisualElement = dragVisual;

      // Store start position for calculations
      this.startX = event.clientX;
    },

    handleActionDragMove(event) {
      if (!this.isDraggingAction || !this.dragVisualElement) return;

      // Move the visual element with the cursor
      this.dragVisualElement.style.left = `${event.clientX}px`;
      this.dragVisualElement.style.top = `${event.clientY}px`;

      // Calculate which frame we're over
      this.calculateDragTargetFrame(event);
    },

    calculateDragTargetFrame(event) {
      // First clear all existing highlights
      document.querySelectorAll(".timeline-cell").forEach((cell) => {
        cell.classList.remove("drag-target");
        cell.classList.remove("drag-target-replace");
      });

      const timelineRect = this.$refs.timelineWrapper.getBoundingClientRect();
      const iconColumnWidth =
        document.querySelector(".icon-column")?.clientWidth || 35;

      // If outside the timeline area horizontally, don't calculate
      if (
        event.clientX < timelineRect.left + iconColumnWidth ||
        event.clientX > timelineRect.right
      ) {
        this.dragTargetFrame = null;
        return;
      }

      // Get the position within the timeline (accounting for scroll)
      const relX =
        event.clientX -
        timelineRect.left -
        iconColumnWidth +
        this.$refs.timelineWrapper.scrollLeft;

      // Calculate which frame we're over based on frame width
      const targetFrame = Math.floor(relX / this.timelineHeaderFrameWidth);

      // Ensure the targetFrame is valid
      if (targetFrame >= 0 && targetFrame < this.totalFrames) {
        this.dragTargetFrame =
          this.draggedActionType === SEND_DATA ? targetFrame + 1 : targetFrame;

        // Check if we will be replacing an action
        const targetFrameData =
          this.currentSequence.frames[this.dragTargetFrame];
        const willReplace = this.hasConflictingActionInFrame(
          targetFrameData,
          this.draggedAction,
          this.draggedActionType,
        );

        // Just manually apply highlight class to all cells at position targetFrame in the relevant rows
        const cssClass = willReplace ? "drag-target-replace" : "drag-target";

        // Target the correct rows based on action type
        const rowSelector = `.${this.getRowClassForActionType(this.draggedActionType)}`;

        // Get all timeline rows in this action type section
        const rows = document.querySelectorAll(`${rowSelector} .timeline-row`);

        rows.forEach((row) => {
          // Get all cells in each row - these should be in frame order (0, 1, 2, ...)
          const cells = row.querySelectorAll(".timeline-cell");

          // Apply highlight to the cell at targetFrame index
          if (cells[targetFrame]) {
            cells[targetFrame].classList.add(cssClass);
          }
        });
      } else {
        this.dragTargetFrame = null;
      }
    },

    getRowClassForActionType(type) {
      switch (type) {
        case "sendData":
          return "sendData-row";
        case "elementPosition":
          return "elementPosition-row";
        case "elementOpacity":
          return "elementOpacity-row";
        case "viewboxPosition":
          return "viewbox-row";
        default:
          return "";
      }
    },

    async finishActionDrag() {
      if (!this.isDraggingAction) return;

      // Clean up the visual element
      if (this.dragVisualElement) {
        document.body.removeChild(this.dragVisualElement);
        this.dragVisualElement = null;
      }

      // Remove all drag-target indicators
      document.querySelectorAll(".timeline-cell").forEach((cell) => {
        cell.classList.remove("drag-target");
        cell.classList.remove("drag-target-replace");
      });

      // If we have a valid target frame and it's different from the original
      if (
        this.dragTargetFrame !== null &&
        this.dragTargetFrame !== this.draggedActionOriginalFrame &&
        this.draggedActionIndex !== -1
      ) {
        const targetFrame = this.currentSequence.frames[this.dragTargetFrame];
        const willReplaceAction = this.hasConflictingActionInFrame(
          targetFrame,
          this.draggedAction,
          this.draggedActionType,
        );

        if (willReplaceAction) {
          // First find and remove the conflicting action in the target frame
          const actionToReplace = this.findConflictingActionInFrame(
            targetFrame,
            this.draggedAction,
            this.draggedActionType,
          );
          if (actionToReplace !== -1) {
            // Remove the conflicting action
            targetFrame.splice(actionToReplace, 1);
          }
        }

        // Now move the action to the target frame
        try {
          await moveActionToFrame(
            this.currentSequence,
            this.draggedActionOriginalFrame,
            this.draggedActionIndex,
            this.dragTargetFrame,
          );

          // Update the view to reflect changes
          setupObjectsAtFrame(
            this.currentSequenceId,
            this.currentSequence.currentFrame,
          );
        } catch (error) {
          console.error("Error moving action to frame:", error);
        }
      }

      // Reset dragging state
      this.resetDragState();
    },

    hasConflictingActionInFrame(frame, action, actionType) {
      if (!frame) return false;

      // Check for conflicts based on action type
      if (actionType === "sendData") {
        return frame.some(
          (existingAction) =>
            existingAction.metadata.type === SEND_DATA &&
            !existingAction.metadata.interpolated &&
            existingAction.data.elementSource === action.data.elementSource &&
            existingAction.data.elementDestination ===
              action.data.elementDestination,
        );
      } else if (actionType === "elementPosition") {
        return frame.some(
          (existingAction) =>
            existingAction.metadata.type === ELEMENT_POSITION &&
            !existingAction.metadata.interpolated &&
            existingAction.data.elementId === action.data.elementId,
        );
      } else if (actionType === "elementOpacity") {
        return frame.some(
          (existingAction) =>
            existingAction.metadata.type === ELEMENT_OPACTITY &&
            !existingAction.metadata.interpolated &&
            existingAction.data.elementId === action.data.elementId,
        );
      } else if (actionType === "viewboxPosition") {
        return frame.some(
          (existingAction) =>
            existingAction.metadata.type === VIEWBOX &&
            !existingAction.metadata.interpolated,
        );
      }

      return false;
    },

    resetDragState() {
      this.isDraggingAction = false;
      this.draggedAction = null;
      this.draggedActionType = null;
      this.draggedActionOriginalFrame = null;
      this.draggedActionIndex = null;
      this.dragTargetFrame = null;
    },

    cancelActionDrag() {
      if (this.dragVisualElement) {
        document.body.removeChild(this.dragVisualElement);
        this.dragVisualElement = null;
      }
      this.resetDragState();
    },
    findActionIndexInFrame(action, frameIndex) {
      if (!this.currentSequence || !this.currentSequence.frames[frameIndex]) {
        return -1;
      }

      const frame = this.currentSequence.frames[frameIndex];

      // First try to find by direct reference (if the action is the same object)
      const directIndex = frame.indexOf(action);
      if (directIndex !== -1) {
        return directIndex;
      }

      // If not found by direct reference, try to find by matching properties
      // This is necessary because the action in the row might be a copy or derived from the original
      return frame.findIndex((frameAction) => {
        // Match by ID if available
        if (action.id && frameAction.id === action.id) {
          return true;
        }

        // For sendData actions
        if (
          action.metadata?.type === "sendData" &&
          frameAction.metadata?.type === "sendData"
        ) {
          return (
            frameAction.data?.elementSource === action.data?.elementSource &&
            frameAction.data?.elementDestination ===
              action.data?.elementDestination
          );
        }

        // For element position/opacity and viewbox actions
        if (frameAction.metadata?.type === action.metadata?.type) {
          return frameAction.data?.elementId === action.data?.elementId;
        }

        return false;
      });
    },
    findConflictingActionInFrame(frame, action, actionType) {
      if (!frame) return -1;

      // Check for conflicts based on action type and return the index
      if (actionType === "sendData") {
        return frame.findIndex(
          (existingAction) =>
            existingAction.metadata.type === SEND_DATA &&
            existingAction.data.elementSource === action.data.elementSource &&
            existingAction.data.elementDestination ===
              action.data.elementDestination,
        );
      } else if (actionType === "elementPosition") {
        return frame.findIndex(
          (existingAction) =>
            existingAction.metadata.type === ELEMENT_POSITION &&
            existingAction.data.elementId === action.data.elementId,
        );
      } else if (actionType === "elementOpacity") {
        return frame.findIndex(
          (existingAction) =>
            existingAction.metadata.type === ELEMENT_OPACTITY &&
            existingAction.data.elementId === action.data.elementId,
        );
      } else if (actionType === "viewboxPosition") {
        return frame.findIndex(
          (existingAction) => existingAction.metadata.type === VIEWBOX,
        );
      }

      return -1;
    },
  },
};
</script>

<style scoped>
.resize-handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px; /* Handle size */
  background: #444;
  cursor: ns-resize; /* Resize cursor */
  z-index: 1001;
}

.timeline-container {
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
  left: 60px;
  right: 0;
  max-height: 400px;
  background-color: #2a2d32;
  border-top: 2px solid #444;
  padding: 10px;
  z-index: 1000;
  box-sizing: border-box;
  margin: 0 !important;
  /* Prevent text selection in the entire timeline area */
  user-select: none;
  -webkit-user-select: none; /* For Safari */
  -ms-user-select: none; /* For older IE/Edge */
}

.timeline-hover-marker {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: rgba(255, 0, 0, 0.2);
  pointer-events: none;
  z-index: 20;
}

.checkboxes-row {
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.speed-control {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.timeline-wrapper-container {
  position: relative;
  overflow-x: auto;
  white-space: nowrap;
}

/* Example styling if you have a timeline table and rows: */
.timeline-table {
  display: table;
  width: 100%;
  border-collapse: collapse;
  position: relative;
}

.timeline-table-row {
  display: table-row;
}

.timeline-table-cell {
  display: table-cell;
  vertical-align: middle;
  border-bottom: 1px solid #555;
}

.icon-column {
  border-right: 1px solid #555;
  border-left: 1px solid #555;
  border-top: 1px solid #555;
  min-width: 35px;
  width: 35px;
  padding: 5px;
}

.frame-column {
  /* For timeline frames and actions */
}

.timeline-header {
  display: flex;
  border-top: 1px solid #555;
}

.timeline-header-frame {
  width: 100px;
  text-align: left;
  color: #ddd;
  font-size: 12px;
  padding: 5px;
  border-left: 1px solid #444;
  cursor: crosshair; /* Resize cursor */
}

.timeline-row {
  display: flex;
  height: 30px;
  align-items: center;
}

.timeline-cell {
  width: 100px;
  position: relative;
  height: 30px;
  box-sizing: border-box;
  background-color: rgba(50, 50, 50, 0.3);
}

.timeline-marker {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: red;
  z-index: 10;
  cursor: col-resize;
}

.timeline-marker::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 20px;
  background: transparent;
  transform: translateX(-50%);
  pointer-events: auto;
}

.timeline-marker:hover::before {
  background: rgba(255, 0, 0, 0.2);
  border-radius: 4px;
}

.timeline-header-section {
  margin: 0 10px;
}

.hoverable {
  position: relative;
  padding-right: 24px; /* Add some space for the delete icon if needed */
}

/* Hide the delete icon by default */
.delete-icon {
  display: none;
  position: absolute;
  top: 50%;
  right: 2px;
  transform: translateY(-50%);
  background: transparent;
  color: red;
}

/* Show the delete icon on action hover */
.hoverable:hover .delete-icon {
  display: inline-flex;
}

.v-btn--icon.v-btn--density-default {
  width: 50px;
}

.timeline-edit-sequence {
  display: flex;
  align-items: center;
  padding: 0 10px;
  background-color: #2a2d32;
  border-top: 1px solid #444;
  height: 36px; /* Matches the toolbar height */
}

.timeline-edit-sequence v-text-field {
  margin: 0;
  padding: 0;
}

.repeatButtonOn {
  color: #6cffd8 !important;
}

.repeatButtonOff {
  color: white !important;
}

@media (max-width: 1200px) {
  .v-btn--icon.v-btn--density-default {
    width: 25px;
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .timeline-container {
    padding: 5px;
    left: 50px;
  }

  .timeline-header-frame {
    width: 50px; /* Smaller frame width for mobile */
    font-size: 9px;
  }

  .hoverable {
    padding-right: 16px; /* Adjust spacing for delete icon */
  }

  .delete-icon {
    right: 0; /* Align delete icon closer on mobile */
  }

  .v-btn--icon.v-btn--density-default {
    width: 20px;
  }
}

@media (max-width: 480px) {
  .timeline-container {
    padding: 2px;
  }

  .timeline-header-frame {
    width: 40px;
    font-size: 8px;
  }
}

/* Drag and drop styling */
.dragging-action-visual {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  background-color: #3a3e45;
  border: 1px solid #6cffd8;
  z-index: 9999;
  transform: scale(1.1);
  transition: transform 0.1s ease;
}

.dragging-action-visual .v-chip {
  margin: 0 2px;
}

/* Add animation for the drag target */
.drag-target {
  animation: pulse-border 1.5s infinite;
}

/* Styling for when we're replacing an action */
.drag-target-replace {
  animation: pulse-replace-border 1.5s infinite;
}

@keyframes pulse-border {
  0% {
    background-color: rgba(100, 255, 100, 0.15);
    border: 1px solid rgba(100, 255, 100, 0.4);
    box-shadow: inset 0 0 10px rgba(100, 255, 100, 0.1);
  }
  50% {
    background-color: rgba(100, 255, 100, 0.4);
    border: 1px solid rgba(100, 255, 100, 0.8);
    box-shadow: inset 0 0 10px rgba(100, 255, 100, 0.3);
  }
  100% {
    background-color: rgba(100, 255, 100, 0.15);
    border: 1px solid rgba(100, 255, 100, 0.4);
    box-shadow: inset 0 0 10px rgba(100, 255, 100, 0.1);
  }
}

@keyframes pulse-replace-border {
  0% {
    background-color: rgba(255, 165, 0, 0.15);
    border: 1px solid rgba(255, 165, 0, 0.4);
    box-shadow: inset 0 0 10px rgba(255, 165, 0, 0.1);
  }
  50% {
    background-color: rgba(255, 165, 0, 0.4);
    border: 1px solid rgba(255, 165, 0, 0.8);
    box-shadow: inset 0 0 10px rgba(255, 165, 0, 0.3);
  }
  100% {
    background-color: rgba(255, 165, 0, 0.15);
    border: 1px solid rgba(255, 165, 0, 0.4);
    box-shadow: inset 0 0 10px rgba(255, 165, 0, 0.1);
  }
}
.imported-sequence-block {
  position: absolute;
  height: 100%;
  background-color: #6c5ce7;
  color: white;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  z-index: 1;
  padding: 0 4px;
  cursor: move;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border: 1px solid #5b4cc7;
}

/* Style for different rows of imported sequences */
.imported-sequences-row .timeline-row:nth-child(2n) .imported-sequence-block {
  background-color: #8c7ae6;
  border-color: #7c6ad6;
}

.imported-sequences-row .timeline-row:nth-child(3n) .imported-sequence-block {
  background-color: #9c8af5;
  border-color: #8c7ae5;
}

.imported-sequences-row .timeline-row:nth-child(4n) .imported-sequence-block {
  background-color: #ac9af5;
  border-color: #9c8af5;
}

.imported-sequence-target {
  background-color: rgba(108, 92, 231, 0.3);
  border: 1px dashed #6c5ce7;
}

.imported-sequence-block:hover {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.delete-imported-sequence {
  position: absolute;
  right: 4px;
  top: 2px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
}

.imported-sequence-block:hover .delete-imported-sequence {
  opacity: 1;
}

.delete-imported-sequence:hover {
  color: #ff6b6b;
}
</style>
