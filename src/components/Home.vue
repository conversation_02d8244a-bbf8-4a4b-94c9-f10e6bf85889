<template>
  <v-app>
    <v-container fluid class="main-wrapper">
      <v-app-bar
        app
        clipped-left
        clipped-right
        dense
        color="#202226"
        height="50"
      >
        <v-container fluid class="header-container">
          <v-row align="center" class="header-row">
            <!-- Logo and Buttons -->
            <div class="title-and-buttons">
              <v-toolbar-title class="app-title">
                <img
                  src="/images/logo.svg"
                  alt="SIMULACTION.IO"
                  class="logo-img"
                />
              </v-toolbar-title>
              <v-btn-toggle
                v-model="currentHeaderTab"
                mandatory
                class="header-buttons"
              >
                <v-btn
                  class="header-button"
                  :class="{ active: currentHeaderTab === 'CREATION' }"
                  prepend-icon="mdi-hammer"
                  @click="toggleCreationMode"
                  id="headerCreation"
                >
                  CREATION
                </v-btn>
                <v-btn
                  class="header-button"
                  :class="{ active: currentHeaderTab === 'ANIMATION' }"
                  prepend-icon="mdi-video-outline"
                  @click="toggleRecordingMode"
                  id="headerAnimation"
                >
                  ANIMATION
                </v-btn>
                <v-btn
                  class="header-button"
                  :class="{ active: currentHeaderTab === 'PRESENTATION' }"
                  prepend-icon="mdi-play"
                  @click="togglePlaybackMode"
                  id="headerPresentation"
                >
                  PRESENTATION
                </v-btn>
              </v-btn-toggle>
            </div>
            <v-spacer></v-spacer>
            <!-- Right: Feedback Button -->
            <v-btn
              prepend-icon="mdi-bullhorn"
              @click="displayFeedbackDialog"
              title="Feedback"
              color="yellow"
            >
              FEEDBACK
            </v-btn>
          </v-row>
        </v-container>
      </v-app-bar>
      <v-system-bar height="30" color="#202226">
        <v-container fluid class="system-bar-container">
          <v-row class="system-bar-row" align="center">
            <!-- Diagram Name -->
            <v-col cols="auto" class="system-bar-left">
              <v-btn
                class="system-bar-btn"
                height="25"
                variant="outlined"
                @click="renameDialog = true"
              >
                {{
                  (dirty ? "*" : "") +
                  diagramName?.substring(0, 17) +
                  (diagramName?.length > 18 ? "..." : "")
                }}
              </v-btn>
            </v-col>

            <!-- Menu Options -->
            <v-col cols="auto" class="system-bar-right">
              <!-- File Menu -->
              <v-menu
                v-model="activeMenu.file"
                open-on-hover
                open-delay="0"
                close-delay="50"
                no-click-animation
                transition="none"
                @mouseenter="openMenu('file')"
              >
                <template v-slot:activator="{ props }">
                  <v-btn
                    v-bind="props"
                    class="system-bar-menu-btn"
                    variant="text"
                    @mouseenter="openMenu('file')"
                    @click="openMenu('file')"
                    id="file-menu-button"
                  >
                    <v-icon left>mdi-file-outline</v-icon> File
                  </v-btn>
                </template>
                <v-list class="menu-list" style="background-color: #202226">
                  <v-list-item @click="newBlankCreation">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-file-plus-outline</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>New Project</v-list-item-title>
                    </div>
                  </v-list-item>
                  <v-list-item @click="displayWelcomePage">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-home-lightbulb</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Welcome page</v-list-item-title>
                    </div>
                  </v-list-item>
                  <v-list-item @click="choseOpenMethod">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-folder-open-outline</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Open</v-list-item-title>
                    </div>
                  </v-list-item>
                  <v-list-item @click="save">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-content-save-outline</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Save</v-list-item-title>
                    </div>
                  </v-list-item>
                  <v-list-item @click="choseSaveMethod">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-content-save-move-outline</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Save As...</v-list-item-title>
                    </div>
                  </v-list-item>
                </v-list>
              </v-menu>

              <!-- Edit Menu -->
              <v-menu
                v-model="activeMenu.edit"
                open-on-hover
                open-delay="0"
                close-delay="50"
                :close-on-content-click="false"
                no-click-animation
                transition="none"
                @mouseenter="openMenu('edit')"
              >
                <template v-slot:activator="{ props }">
                  <v-btn
                    v-bind="props"
                    class="system-bar-menu-btn"
                    variant="text"
                    @mouseenter="openMenu('edit')"
                    @click="openMenu('edit')"
                    :disabled="this.diagramMode === 'PLAYBACK_MODE'"
                  >
                    <v-icon left>mdi-pencil-outline</v-icon> Edit
                  </v-btn>
                </template>
                <v-list class="menu-list" style="background-color: #202226">
                  <v-list-item @click="selectAllElements()">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-selection-multiple</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Select All Elements</v-list-item-title>
                    </div>
                  </v-list-item>
                  <v-list-item @click="undo" :disabled="!canUndo">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-undo</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Undo</v-list-item-title>
                    </div>
                  </v-list-item>
                  <v-list-item @click="redo" :disabled="!canRedo">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-redo</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Redo</v-list-item-title>
                    </div>
                  </v-list-item>
                </v-list>
              </v-menu>

              <!-- Help Menu -->
              <v-menu
                v-model="activeMenu.help"
                open-on-hover
                open-delay="0"
                close-delay="50"
                no-click-animation
                transition="none"
                @mouseenter="openMenu('help')"
                @click="openMenu('help')"
              >
                <template v-slot:activator="{ props }">
                  <v-btn
                    v-bind="props"
                    class="system-bar-menu-btn"
                    variant="text"
                    @mouseenter="openMenu('help')"
                  >
                    <v-icon left>mdi-help-circle-outline</v-icon> Help
                  </v-btn>
                </template>
                <v-list class="menu-list" style="background-color: #202226">
                  <v-list-item @click="openTutorialDialog">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-school-outline</v-icon>
                      </v-list-item-icon>
                      <v-list-item-title>Start Tutorial</v-list-item-title>
                    </div>
                  </v-list-item>
                  <v-list-item @click="openTourguide">
                    <div class="menu-list-item">
                      <v-list-item-icon class="list-item-icon">
                        <v-icon>mdi-walk</v-icon>
                        <!-- You can choose an appropriate icon -->
                      </v-list-item-icon>
                      <v-list-item-title>Show Tour Guide</v-list-item-title>
                    </div>
                  </v-list-item>
                </v-list>
              </v-menu>

              <!-- Preferences Menu -->
              <v-menu
                v-model="activeMenu.preferences"
                open-on-hover
                open-delay="0"
                close-delay="50"
                no-click-animation
                transition="none"
                :close-on-content-click="false"
                @mouseenter="openMenu('preferences')"
                @click="openMenu('preferences')"
              >
                <template v-slot:activator="{ props }">
                  <v-btn
                    v-bind="props"
                    class="system-bar-menu-btn"
                    variant="text"
                    @mouseenter="openMenu('preferences')"
                  >
                    <v-icon left>mdi-heart-settings-outline</v-icon> Preferences
                  </v-btn>
                </template>
                <v-list class="menu-list" style="background-color: #202226">
                  <v-list-item>
                    <v-switch
                      v-model="displayShadows"
                      @click="updateDisplayShadows"
                      label="Display Shadows"
                      color="blue"
                    ></v-switch>
                  </v-list-item>
                  <v-list-item>
                    <v-switch
                      v-model="displayGrid"
                      @click="updateDisplayGrid"
                      label="Display Grid"
                      color="blue"
                    ></v-switch>
                  </v-list-item>
                  <v-list-item>
                    <v-switch
                      v-model="snapToGrid"
                      @click="updateSnapToGrid"
                      label="Snap to Grid"
                      color="blue"
                    ></v-switch>
                  </v-list-item>
                  <v-divider />
                  <v-list-item>
                    <v-switch
                      v-model="autoIncrementFrame"
                      @click="updateAutoIncrementFrame"
                      label="Auto Increment Frame"
                      color="blue"
                    ></v-switch>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-col>
          </v-row>
        </v-container>
      </v-system-bar>

      <!-- Workspace Breadcrumb Bar -->
      <v-system-bar
        v-if="hasMultipleWorkspaces && diagramMode !== 'PLAYBACK_MODE'"
        height="30"
        color="#202226"
        class="breadcrumb-bar"
      >
        <v-container fluid class="breadcrumb-container">
          <v-btn
            v-if="hasParentWorkspace"
            icon="mdi-arrow-left"
            color="white"
            variant="text"
            @click="navigateToParent"
            class="back-button"
          ></v-btn>
          <WorkspaceBreadcrumb />
        </v-container>
      </v-system-bar>

      <Toolbar />
      <RightDrawer />
      <v-btn
        absolute
        icon
        @click.stop="toggleShowRightDrawer"
        class="floatingButton"
        fab
        color="#2a2d32ff"
        v-show="this.diagramMode !== 'PLAYBACK_MODE'"
      >
        <v-icon icon="mdi-chevron-left" color="white"></v-icon>
      </v-btn>
      <TimelineComponent
        v-show="this.diagramMode === 'RECORDING_MODE' && this.currentSequenceId"
      />
      <SimpleTimelineComponent
        v-show="this.diagramMode === 'PLAYBACK_MODE' && this.currentSequenceId"
        style="left: 60px"
      />
      <v-main>
        <SVGMain />
      </v-main>
    </v-container>
  </v-app>
  <MobileComingSoon
    v-if="$vuetify.display.smAndDown"
    v-model="displayMobileComingSoon"
  />
  <Welcome
    v-if="!$vuetify.display.smAndDown"
    v-model="displayWelcome"
    @choseOpenMethodDialog="choseOpenMethodDialog = $event"
    @updateWelcomeDialog="displayWelcome = $event"
    @updateTutorialDialog="openTutorial = $event"
  />
  <RenameDiagramDialog
    v-model="renameDialog"
    @updateRenameDialog="renameDialog = $event"
  />
  <ChoseSaveMethod
    v-model="choseSaveMethodDialog"
    @updateChoseSaveMethodDialog="choseSaveMethodDialog = $event"
  />
  <ChoseOpenMethod
    v-model="choseOpenMethodDialog"
    @updateOpenFromLocalStorageDialog="openFromLocalStorageDialog = $event"
    @updateChoseOpenMethodDialog="choseOpenMethodDialog = $event"
  />
  <OpenFromLocalStorage
    @updateOpenFromLocalStorageDialog="openFromLocalStorageDialog = $event"
    v-model="openFromLocalStorageDialog"
  />
  <Tutorial
    @updateTutorialDialog="openTutorial = $event"
    @updateTourGuide="updateTourGuide($event)"
    v-show="openTutorial"
    :isVisible="openTutorial"
  />
  <FeedbackDialog
    v-model="feedbackDialog"
    @updateFeedbackDialog="feedbackDialog = $event"
    @feedbackSuccess="handleFeedbackSuccess"
  />
  <TourGuide
    @updateTourGuide="updateTourGuide($event)"
    :isVisible="openTour || manuallyOpenTour"
    v-show="openTour || manuallyOpenTour"
  />
  <MoveElementDialog
    v-model="moveElementDialogVisible"
    :connectedCount="moveElementDialogConnectedCount"
    @decision="handleMoveElementDialogDecision"
  />
  <!-- Snackbar for success message -->
  <v-snackbar v-model="snackbar" :timeout="3000">
    {{ snackbarMessage }}
    <template v-slot:actions="{ attrs }">
      <v-btn color="green" text v-bind="attrs" @click="snackbar = false">
        Close
      </v-btn>
    </template>
  </v-snackbar>
</template>

<script>
import SVGMain from "@/components/SVGMain";
import { mapActions, mapState } from "vuex";
import RightDrawer from "@/components/RightDrawer.vue";
import Toolbar from "@/components/Toolbar.vue";
import {
  changeBackground,
  enterWorkspaceWithZoomOut,
  isEmptyObject,
  redo,
  resetAll,
  saveWorkspace,
  selectAllElements,
  toggleCreationMode,
  togglePlaybackMode,
  toggleRecordingMode,
  undo,
  updateGraphicElementFromState,
} from "@/core";
import RenameDiagramDialog from "@/components/RenameDiagramDialog.vue";
import ChoseSaveMethod from "@/components/ChoseSaveMethod.vue";
import { saveDefault } from "@/files";
import ChoseOpenMethod from "@/components/ChoseOpenMethod.vue";
import OpenFromLocalStorage from "@/components/OpenFromLocalStorage.vue";
import openFromLocalStorage from "@/components/OpenFromLocalStorage.vue";
import Tutorial from "@/components/Tutorial.vue";
import TimelineComponent from "@/components/Timeline.vue";
import SimpleTimelineComponent from "@/components/SimpleTimeline.vue";
import Welcome from "@/components/Welcome.vue";
import FeedbackDialog from "@/components/FeedbackDialog.vue";
import MobileComingSoon from "@/components/MobileComingSoon.vue";
import { forceStopAnimation } from "@/animation";
import TourGuide from "@/components/TourGuide.vue";
import WorkspaceBreadcrumb from "@/components/WorkspaceBreadcrumb.vue";
import MoveElementDialog from "@/components/MoveElementDialog.vue";
import { v4 as uuidv4 } from "uuid";

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Home",
  methods: {
    // Handle the decision from the MoveElementDialog component
    handleMoveElementDialogDecision(decision) {
      // Call the resolve function with the user's decision
      if (this.moveElementDialogResolve) {
        this.moveElementDialogResolve(decision);
        this.moveElementDialogResolve = null;
      }
    },
    selectAllElements,
    redo,
    undo,
    isEmptyObject,
    ...mapActions([
      "showRightDrawerAction",
      "updateDiagramNameAction",
      "updateDirtyAction",
      "updateHeaderTabAction",
      "updateSnapToGridAction",
      "updateDisplayGridAction",
      "updateDisplayShadowsAction",
      "updateAutoIncrementFrameAction",
      "setDiagramIdAction",
    ]),
    toggleShowRightDrawer() {
      this.showRightDrawerAction(!this.showRightDrawer);
    },
    async toggleRecordingMode() {
      await toggleRecordingMode();
    },
    togglePlaybackMode() {
      togglePlaybackMode();
    },
    async toggleCreationMode() {
      await toggleCreationMode();
    },
    displayWelcomePage() {
      this.displayWelcome = true;
    },
    displayFeedbackDialog() {
      this.feedbackDialog = true;
    },
    async newBlankCreation() {
      if (this.dirty) {
        const confirm = window.confirm(
          "You have un-saved changes. Do you still want to proceed?",
        );
        if (!confirm) {
          return;
        }
      }
      forceStopAnimation();
      this.openTutorial = false;
      await resetAll();
      this.setDiagramIdAction(uuidv4());
      this.updateDiagramNameAction("New Diagram");
      this.updateDirtyAction(false);
    },
    openTutorialDialog() {
      this.openTutorial = true;
      this.openTour = false;
    },
    openTourguide() {
      this.manuallyOpenTour = true;
      this.openTutorial = false;
      localStorage.setItem("tourDismissed", "false");
    },
    async save() {
      if (!saveDefault) {
        this.choseSaveMethodDialog = true;
      } else {
        saveWorkspace().then();
      }
    },
    choseSaveMethod() {
      this.choseSaveMethodDialog = true;
    },
    choseOpenMethod() {
      this.choseOpenMethodDialog = true;
    },
    openMenu(menu) {
      // Close all menus
      Object.keys(this.activeMenu).forEach((key) => {
        this.activeMenu[key] = false;
      });
      // Open the hovered menu
      this.activeMenu[menu] = true;
    },
    updateDisplayGrid() {
      let newDisplayGridValue = !this.displayGrid;
      this.updateDisplayGridAction(newDisplayGridValue);
      changeBackground();
    },
    updateDisplayShadows() {
      let newDisplayShadowsValue = !this.displayShadows;
      this.updateDisplayShadowsAction(newDisplayShadowsValue);
      Array.from(this.allSimpleElements.keys()).forEach((elementId) => {
        updateGraphicElementFromState(elementId);
      });
    },
    updateSnapToGrid() {
      this.updateSnapToGridAction(!this.snapToGrid);
    },
    updateAutoIncrementFrame() {
      this.updateAutoIncrementFrameAction(!this.autoIncrementFrame);
    },
    handleFeedbackSuccess(message) {
      this.snackbarMessage = message;
      this.snackbar = true;
    },
    shouldOpenTour() {
      this.openTour = localStorage.getItem("tourDismissed") !== "true";
    },
    updateTourGuide(value) {
      this.openTour = value;
      this.manuallyOpenTour = value;
    },
    async navigateToParent() {
      const parentElement = Array.from(this.allSimpleElements.values()).find(
        (element) => element.childWorkspaceId === this.currentWorkspaceId,
      );
      await enterWorkspaceWithZoomOut(parentElement.parentWorkspaceIds[0]);
    },
  },
  components: {
    MobileComingSoon,
    FeedbackDialog,
    Welcome,
    SimpleTimelineComponent,
    TimelineComponent,
    Tutorial,
    OpenFromLocalStorage,
    ChoseOpenMethod,
    ChoseSaveMethod,
    RenameDiagramDialog,
    RightDrawer,
    Toolbar,
    SVGMain,
    TourGuide,
    WorkspaceBreadcrumb,
    MoveElementDialog,
  },
  computed: {
    openFromLocalStorage() {
      return openFromLocalStorage;
    },
    ...mapState({
      allSimpleElements: (state) => state.allSimpleElements,
      sequences: (state) => state.sequences,
      currentSequenceId: (state) => state.currentSequenceId,
      selectedElementsIds: (state) => state.selectedElementsIds,
      selectedTextsIds: (state) => state.selectedTextsIds,
      selectedSegmentsIds: (state) => state.selectedSegmentsIds,
      millisecondsPerProcessingUnit: (state) =>
        state.millisecondsPerProcessingUnit,
      diagramName: (state) => state.diagramName,
      diagramMode: (state) => state.diagramMode,
      snapshots: (state) => state.snapshots,
      recordingVideo: (state) => state.recordingVideo,
      showRightDrawer: (state) => state.showRightDrawer,
      showEditionBar: (state) => state.showEditionBar,
      dirty: (state) => state.dirty,
      viewbox: (state) => state.viewbox,
      headerTab: (state) => state.headerTab,
      snapToGrid: (state) => state.snapToGrid,
      displayGrid: (state) => state.displayGrid,
      displayShadows: (state) => state.displayShadows,
      autoIncrementFrame: (state) => state.autoIncrementFrame,
      workspaces: (state) => state.workspaceIds,
      currentWorkspaceId: (state) => state.currentWorkspaceId,
      rootWorkspaceId: (state) => state.rootWorkspaceId,
    }),

    currentHeaderTab: {
      get: function () {
        return this.headerTab;
      },
      set: function (value) {
        this.updateHeaderTabAction(value);
      },
    },
    canUndo() {
      return this.snapshots.length > 1 && !this.snapshots[0].active;
    },
    canRedo() {
      return !this.snapshots[this.snapshots.length - 1].active;
    },
    hasMultipleWorkspaces() {
      return this.workspaces.length > 1;
    },
    hasParentWorkspace() {
      if (this.currentWorkspaceId === this.rootWorkspaceId) return false;

      const parentElement = Array.from(this.allSimpleElements.values()).find(
        (element) => element.childWorkspaceId === this.currentWorkspaceId,
      );
      return parentElement?.parentWorkspaceIds?.length > 0;
    },
  },
  beforeMount() {
    this.shouldOpenTour();
  },
  data: function () {
    return {
      currentFrameSliderValue: 0,
      tabs: null,
      editingSequenceName: false,
      editingElementName: false,
      editingTextContent: false,
      currentSequenceDisplayOffFrame: null,
      currentSequenceDisplayOnFrame: null,
      newDialog: false,
      renameDialog: false,
      choseSaveMethodDialog: false,
      choseOpenMethodDialog: false,
      openFromLocalStorageDialog: false,
      openTutorial: false,
      manuallyOpenTour: false,
      openTour: true,
      displayWelcome: true,
      feedbackDialog: false,
      moveElementDialogVisible: false,
      moveElementDialogConnectedCount: 0,
      moveElementDialogResolve: null,
      activeMenu: {
        file: false,
        edit: false,
        help: false,
        preferences: false,
      },
      snackbar: false,
      snackbarMessage: "",
      displayMobileComingSoon: true,
    };
  },
  created() {
    // Listen for the custom event from core.js
    document.addEventListener('showMoveElementDialog', (event) => {
      this.moveElementDialogConnectedCount = event.detail.connectedCount;
      this.moveElementDialogVisible = true;

      // Store the resolve function to call when the user makes a decision
      this.moveElementDialogResolve = event.detail.resolve;
    });
  },
};
</script>
<style scoped>
.floatingButton {
  z-index: 100;
  position: fixed;
  top: 80px;
  right: 0;
  background-color: grey;
}
.main-wrapper {
  max-width: 100vw; /* Prevent content from exceeding screen width */
  overflow-x: hidden; /* Hide horizontal overflow */
  padding: 0;
}

.v-navigation-drawer {
  height: 100%;
}

.v-main {
  overflow-y: auto;
  padding: 0;
}

.header-container {
  padding: 0 1rem;
}

.header-row {
  flex-wrap: nowrap;
}

.title-col {
  flex-shrink: 0;
}

.app-title {
  color: white;
  margin-right: 1rem;
}

/* Buttons */
.header-buttons {
  display: flex;
  gap: 0.5rem; /* Space between buttons */
  flex-shrink: 1;
}

.header-button {
  font-size: 0.9rem;
  color: white;
  text-transform: uppercase;
  background-color: transparent;
  transition:
    background-color 0.2s,
    color 0.2s;
  min-width: 220px;
  padding: 0.4rem 0.8rem;
  flex-shrink: 1; /* Shrink to fit smaller screens */
  white-space: nowrap; /* Prevent text wrapping */
}

.header-button:hover {
  background-color: #444;
}

.header-button.active {
  color: #fff;
  background-color: #444;
}

.title-and-buttons {
  display: flex;
  align-items: center;
  gap: 1rem; /* Space between logo and buttons */
  flex-wrap: wrap;
  overflow: hidden;
}

.logo-img {
  max-height: 40px; /* Ensure logo scales properly */
  height: auto;
  max-width: 100%; /* Prevent overflow */
  margin-left: 10px;
  margin-top: 10px;
}

/* System Bar Layout */
.system-bar-container {
  display: flex;
  align-items: center;
}

.system-bar-row {
  flex-wrap: nowrap;
  justify-content: flex-start; /* Align everything to the left */
  padding: 0 0.5rem;
  width: 100%;
}

.system-bar-left {
  flex-shrink: 0;
  margin-right: 1rem; /* Space between diagram name and menus */
}

.system-bar-right {
  display: flex;
  gap: 0.5rem; /* Space between menu items */
}

.system-bar-btn {
  font-size: 0.8rem;
  color: white;
  background-color: #202226;
  text-transform: uppercase;
  padding: 0.2rem 0.5rem;
  height: 25px;
}

.system-bar-menu-btn {
  font-size: 0.8rem;
  color: white;
  background-color: #202226;
  text-transform: uppercase;
  padding: 0 0.5rem;
  height: 25px;
}

/* Menu Styling */
.menu-list {
  min-width: 200px;
  color: #ffffff;
}

/* Adjust the button styles */
.system-bar-menu-btn {
  color: white;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.list-item-icon {
  margin-right: 10px;
}

.menu-list-item {
  display: flex;
}

.dialog {
  background-color: rgba(145, 150, 158, 0.6);
  backdrop-filter: blur(2px);
}

@media (max-width: 768px) {
  .main-wrapper {
    padding: 0;
  }
  .v-navigation-drawer {
    width: 50px; /* Adjust for mobile */
  }
  .title-and-buttons {
    gap: 0.5rem; /* Reduce gap on small screens */
  }
  .header-row {
    padding: 0;
  }
  .header-container {
    padding: 0 0.5rem;
  }

  .system-bar-menu-btn {
    min-width: 45px;
  }

  .app-title {
    font-size: 1rem;
    margin-right: 0.5rem;
  }

  .header-buttons {
    gap: 0.3rem; /* Reduce gap between buttons */
    flex-grow: 1; /* Ensure buttons stretch to fit available space */
  }

  .header-button {
    font-size: 0.6rem;
    padding: 0.3rem 0.4rem; /* Smaller buttons */
    min-width: 60px; /* Reduce width */
    max-width: 100px;
  }

  .logo-img {
    max-height: 30px; /* Adjust logo size for small screens */
    margin-right: 0.5rem;
  }

  .v-menu {
    width: 100%; /* Ensure dropdowns fit on screen */
  }

  .v-toolbar-title {
    min-width: 45px;
    max-width: 45px;
  }
}

.system-bar-breadcrumb {
  flex-grow: 1;
  margin: 0 16px;
  overflow: hidden;
}

.breadcrumb-bar {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.breadcrumb-container {
  padding: 0 16px;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 8px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.back-button:hover {
  opacity: 1;
}
</style>
