<template>
  <div>
    <div :style="overlayStyle"></div>
    <v-card :style="cardStyle">
      <v-card-title class="tutorial-title"> Simulaction Tutorial </v-card-title>

      <!-- Current section header -->
      <div class="current-section-header">
        <v-chip color="primary" size="large">
          <v-icon left>{{ getCurrentSectionIcon }}</v-icon>
          {{ getCurrentSectionName }}
        </v-chip>
      </div>

      <div v-if="currentStepIndex === 0" class="tutorial-sections">
        <p>Welcome to the Simulaction Tutorial!</p>
        <p>
          This tutorial will guide you through all the features of Simulaction.
        </p>
        <p>
          You can either follow the tutorial step by step, or jump to a specific
          section:
        </p>

        <div class="section-buttons">
          <v-btn
            text
            @click="skipToSection('welcome')"
            color="primary"
            class="section-btn"
          >
            <v-icon left>mdi-home-outline</v-icon>
            Welcome
          </v-btn>
          <v-btn
            text
            @click="skipToSection('creation')"
            color="primary"
            class="section-btn"
          >
            <v-icon left>mdi-shape-outline</v-icon>
            Creation
          </v-btn>
          <v-btn
            text
            @click="skipToSection('connection')"
            color="primary"
            class="section-btn"
          >
            <v-icon left>mdi-connection</v-icon>
            Connection
          </v-btn>
          <v-btn
            text
            @click="skipToSection('animation')"
            color="primary"
            class="section-btn"
          >
            <v-icon left>mdi-animation-outline</v-icon>
            Animation
          </v-btn>
          <v-btn
            text
            @click="skipToSection('timeline')"
            color="primary"
            class="section-btn"
          >
            <v-icon left>mdi-timeline</v-icon>
            Timeline
          </v-btn>
          <v-btn
            text
            @click="skipToSection('presentation')"
            color="primary"
            class="section-btn"
          >
            <v-icon left>mdi-presentation</v-icon>
            Presentation
          </v-btn>
          <v-btn
            text
            @click="skipToSection('shortcuts')"
            color="primary"
            class="section-btn"
          >
            <v-icon left>mdi-keyboard-outline</v-icon>
            Shortcuts
          </v-btn>
          <v-btn
            text
            @click="skipToSection('extras')"
            color="primary"
            class="section-btn"
          >
            <v-icon left>mdi-information-outline</v-icon>
            Extras
          </v-btn>
        </div>

        <div class="tutorial-navigation">
          <v-btn color="primary" @click="nextStep">
            Start Tutorial
            <v-icon right>mdi-arrow-right</v-icon>
          </v-btn>
        </div>
      </div>

      <p style="white-space: normal">{{ currentStep.text }}</p>

      <v-card-text>
        <v-container>
          <v-row>
            <v-col>
              <v-btn
                v-show="this.currentStepIndex > 0"
                @click="restart"
                icon="mdi-restart"
                fab
              >
                <v-icon icon="mdi-restart" />
                <v-tooltip activator="parent" location="bottom"
                  >Reset</v-tooltip
                >
              </v-btn>
              <v-btn
                v-show="this.currentStepIndex > 0"
                @click="previousFrame"
                icon="mdi-skip-previous"
                fab
              >
                <v-icon icon="mdi-skip-previous" />
                <v-tooltip activator="parent" location="bottom"
                  >Previous Step</v-tooltip
                >
              </v-btn>
              <v-btn
                v-show="this.currentStepIndex < this.steps.length - 1"
                @click="nextFrame"
                icon="mdi-skip-next"
                fab
                id="tutorialNextStepButton"
              >
                <v-icon icon="mdi-skip-next" />
                <v-tooltip activator="parent" location="bottom"
                  >Next Step</v-tooltip
                >
              </v-btn>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-btn @click="close">Close</v-btn>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <v-card-subtitle
        style="
          color: #8eff7d;
          font-size: large;
          font-weight: bold;
          white-space: normal;
        "
      >
        {{ currentStep.successMessage }}
      </v-card-subtitle>

      <!-- Workspace Loader Button -->
      <div v-if="currentStep.workspaceUrl" class="workspace-loader">
        <p>Please load the example workspace to complete this step.</p>
        <v-btn
          color="primary"
          @click="loadWorkspaceForStep"
          :loading="isLoading"
          variant="outlined"
        >
          <v-icon left>mdi-download</v-icon>
          Load Example Workspace
          <v-tooltip activator="parent" location="bottom">
            This will load a pre-configured workspace with the necessary
            elements to complete this step of the tutorial.
          </v-tooltip>
        </v-btn>
        <v-alert v-if="workspaceError" type="error" dense class="mt-2">
          {{ workspaceError }}
        </v-alert>
      </div>
    </v-card>
  </div>
</template>

<script>
import { mapActions, mapState } from "vuex";
import {
  fetchWorkspace,
  holdWorkspace,
  loadWorkspaceFromDeserialisedContent,
  resetAll,
} from "@/core";
import {
  ELEMENT_OPACTITY,
  ELEMENT_POSITION,
  SEND_DATA,
  VIEWBOX,
} from "@/sequence";

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Tutorial",
  props: ["isVisible"],
  data() {
    return {
      currentStepIndex: 0,
      highlightStyle: {},
      isLoading: false,
      workspaceError: null,
      previousActionCount: null,
      previousFrameCount: null,
      elementsBeforeShapeSelection: 0,
      displayTimelineState: null,
      manageSequencesActive: false,
      sectionMarkers: {
        welcome: 0,
        creation: 1,
        connection: 9,
        animation: 13,
        timeline: 20,
        presentation: 28,
        shortcuts: 32,
        extras: 33,
      },
      overlayStyle: {
        position: "fixed",
        top: "0",
        left: "0",
        width: "100%",
        height: "100%",
        background: "rgba(0, 0, 0, 0.7)",
        zIndex: "1500",
      },
      cardStyle: {
        textAlign: "center",
        width: "400px",
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        zIndex: "2000",
        color: "#b1dcdd",
        backgroundColor: "#1c1f22",
      },
      steps: [
        // 0 - Welcome
        {
          text: "",
          overlay: true,
          position: { top: 50, left: 50 },
        },

        // 1 - CREATION SECTION
        {
          text: "Let's begin with creating elements. In this section, you'll learn how to create and customize elements in your diagram.",
          overlay: true,
          position: { top: 50, left: 50 },
        },
        // 2 - Double-click to create element
        {
          text: "Double-click anywhere on the grid to create your first element. This is the quickest way to add a new element to your diagram.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 3 - Select shape and drag-drop
        {
          text: "You can also select a shape first. Click the shape button highlighted on the left toolbar, choose a shape, then drag and drop it onto the canvas.",
          element: "shapeButton",
          overlay: false,
          position: { top: 30, left: 20 },
        },
        // 4 - Select shape and double-click
        {
          text: "Another option: Select a shape from the toolbar, then double-click on the canvas to create it. This is useful when you want to place multiple elements of the same type.",
          element: "shapeButton",
          overlay: false,
          position: { top: 30, left: 20 },
        },
        // 5 - Scale object
        {
          text: "You can resize any element by selecting it and dragging the scaling handle at the bottom-right corner. Try resizing an element now.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 6 - Rename element
        {
          text: "To rename an element, simply double-click on it and type in the new name. A cross will appear, enabling you to reposition the text as needed. Click outside the element to confirm the new name and finalize its placement.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 7 - Update colors
        {
          text: "You can customize the colors of your elements. With an element selected, go to the Color tab in the right panel. Experiment with different fill and text colors.",
          overlay: false,
          position: { top: 50, left: 70 },
          element: "elementsTab",
        },
        // 8 - Update font size
        {
          text: "To adjust text properties, select an element and navigate to the Text tab in the right panel. In this section, you can change the font size, family, and weight. Additionally, you can rename the element by clicking the pencil icon.",
          overlay: false,
          position: { top: 50, left: 70 },
          element: "elementsTab",
        },

        // 9 - CONNECTION SECTION
        {
          text: "Now let's learn how to connect elements. Connections are essential for creating meaningful diagrams and animations.",
          overlay: true,
          position: { top: 50, left: 50 },
          workspaceUrl: "/tutorial-workspaces/elements-to-connect.smlx",
        },
        // 10 - Connect existing elements
        {
          text: "To connect two existing elements, click and drag from a connector point (the small black dots) on one element to another element.",
          overlay: false,
          position: { top: 50, left: 80 },
        },
        // 11 - Create connected element
        {
          text: "You can create a new connected element by dragging from a connector to an empty space on the canvas. This automatically creates a new element and connects it.",
          overlay: false,
          position: { top: 50, left: 80 },
        },
        // 12 - Change connector shape
        {
          text: "To modify the connector style between linear and staircase modes, simply double-click on the connector line. This allows you to select the most appropriate visual style for your diagram, especially when the segments are not aligned horizontally or vertically.",
          overlay: false,
          position: { top: 50, left: 80 },
        },

        // 13 - ANIMATION SECTION
        {
          text: "Let's move on to animation. Simulaction makes it easy to create dynamic visual presentations of your diagrams.",
          overlay: true,
          position: { top: 50, left: 50 },
          workspaceUrl: "/tutorial-workspaces/make-animation.smlx",
        },
        // 14 - Data flow animation
        {
          text: "Please click ANIMATION at the top of the screen to enter animation mode.",
          overlay: false,
          position: { top: 50, left: 50 },
          element: "headerAnimation",
        },
        // 15 - Drag drop data flow
        {
          text: "To animate data flow between elements, click and drag from one element to a connected element. This creates a data flow animation. Notice how the frame number automatically increases. Create several data flows, then press Play to see the animation. Note that the frame auto-increment feature can be disabled from the Preferences menu if you prefer to control frame numbers manually.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 16 - Move non-connected elements
        {
          text: "You can also animate non-connected elements. Use the playback controls to navigate to frame 3, then move an element (not connected to anything) to a new position.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 17 - Continue element movement
        {
          text: "Now move to frame 6 and move the same element again. Play the animation and notice the element smoothly moving between these positions.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 18 - Hide/show elements
        {
          text: "You can make elements appear or disappear at specific frames. Select an element, then click the red eye icon to hide it at the current frame. Hidden elements appear transparent in animation mode but are invisible in presentation mode. Click the eye icon again on a different frame to show the element.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 19 - Camera animation
        {
          text: "You can also animate the view itself. Navigate to a frame, position the view as desired, then click the green camera button. Move to another frame and repeat. This creates cinematic camera movements in your presentation. Click the play button in the playback controls to see the animation.",
          overlay: false,
          position: { top: 50, left: 70 },
        },

        // 20 - TIMELINE SECTION
        {
          text: "Now let's explore the Timeline feature. The timeline is essential for managing and organizing your animation sequences.",
          overlay: true,
          position: { top: 50, left: 50 },
          workspaceUrl: "/tutorial-workspaces/timeline.smlx",
        },
        // 21 - Timeline categories
        {
          text: "The timeline displays different categories of actions: data flow (dots moving between elements), element position (when elements move), element opacity (when elements appear/disappear), and camera position (view changes).",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 22 - Element highlighting
        {
          text: "Highlighting works both ways: hovering over an element in the timeline highlights it on the canvas, and selecting an element on the canvas highlights its actions in the timeline. Try selecting different elements now.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 23 - Display/hide timeline
        {
          text: "You can toggle the timeline visibility by clicking the 'Display Timeline' checkbox in the top toolbar. This gives you more space to work with when not actively managing animations.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 24 - Delete sequence action
        {
          text: "To delete a specific action, hover over the action and click the red bin that appears. This allows precise control over your animation sequence.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 25 - Insert new frame
        {
          text: "To insert a new frame, click the '+' button at the top of the timeline. This creates space for new animation steps without affecting existing ones.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 26 - Delete frame
        {
          text: "To delete a frame, click the '-' button at the top of the timeline. This removes that frame and all its actions from the sequence.",
          overlay: false,
          position: { top: 50, left: 70 },
        },
        // 27 - Manage sequences
        {
          text: "If you tick the 'Manage Sequences' checkbox, you will see a new line appear allowing to manage sequences. The dropdown allows you to navigate between sequences. The pencil allows you to rename the current sequence. The '+' button allows you to create a new empty sequence. The bin button allows you to delete the current sequence. Click on the Sequences tab to rename, add, or delete animation sequences.",
          overlay: false,
          position: { top: 50, left: 70 },
        },

        // 28 - PRESENTATION SECTION
        {
          text: "Now let's explore Presentation mode, where you can view and share your completed animations.",
          overlay: true,
          position: { top: 50, left: 50 },
          workspaceUrl: "/tutorial-workspaces/presentation.smlx",
        },
        // 29 - Basic playback
        {
          text: "Click PRESENTATION at the top of the screen to enter presentation mode. Press Play to watch your animation, or click directly on the timeline to jump to specific frames. You can also drag the red marker to scrub through the animation.",
          overlay: false,
          position: { top: 50, left: 70 },
          element: "headerPresentation",
        },
        // 30 - Director's view
        {
          text: "Click the Director's View button to enable and disable automatic camera movements based on the camera positions you defined. This creates a more dynamic viewing experience.",
          overlay: false,
          position: { top: 50, left: 70 },
          element: "directorViewButton",
        },
        // 31 - Continuous mode
        {
          text: "Try the Continuous Mode option to make data flows play continuously in a loop. Note that this only affects data flows - elements won't move or change visibility and camera's position will remain fixed.",
          overlay: false,
          position: { top: 50, left: 70 },
          element: "continuousModeButton",
        },

        // 32 - SHORTCUTS SECTION
        {
          text: "Keyboard shortcuts can significantly enhance your productivity in Simulaction.",
          overlay: true,
          position: { top: 50, left: 50 },
        },
        // 33 - List all shortcuts
        {
          text: "Essential shortcuts include: DELETE to remove selected elements, CTRL+Z to undo, CTRL+SHIFT+Z to redo, SPACE to start and pause the animation, CTRL+S to save, ESC to cancel operations, Arrow keys to move elements, and CTRL+Arrow keys to resize elements by 1px increments.",
          overlay: false,
          position: { top: 50, left: 50 },
        },

        // 34 - EXTRAS SECTION
        {
          text: "Finally, let's explore some additional features that make Simulaction even more powerful.",
          overlay: true,
          position: { top: 50, left: 50 },
        },
        // 35 - Share diagram
        {
          text: "You can share your animations by clicking the Share option. This generates a link to your animation in the Simulaction player, which can be embedded in websites, documentation, or presentations.",
          overlay: false,
          position: { top: 50, left: 50 },
          element: "shareButton",
        },
        // 36 - Record screen
        {
          text: "The experimental Screen Recording feature allows you to capture your canvas activity as a video. This is useful for creating tutorials or demonstrations of your diagrams and animations.",
          overlay: false,
          position: { top: 50, left: 50 },
          element: "recordVideoButton",
        },
        // 37 - Conclusion
        {
          text: "Congratulations! You've completed the Simulaction tutorial. You now have the knowledge to create professional, dynamic diagrams and animations. Continue exploring the application to discover more features and capabilities.",
          overlay: true,
          position: { top: 50, left: 50 },
        },
      ],
      workspaceBefore: {},
      tutorialWorkspace: {},
    };
  },
  computed: {
    currentStep() {
      return this.steps[this.currentStepIndex];
    },
    getCurrentSectionName() {
      if (this.currentStepIndex >= this.sectionMarkers.extras) return "Extras";
      if (this.currentStepIndex >= this.sectionMarkers.shortcuts)
        return "Shortcuts";
      if (this.currentStepIndex >= this.sectionMarkers.presentation)
        return "Presentation";
      if (this.currentStepIndex >= this.sectionMarkers.timeline)
        return "Timeline";
      if (this.currentStepIndex >= this.sectionMarkers.animation)
        return "Animation";
      if (this.currentStepIndex >= this.sectionMarkers.connection)
        return "Connection";
      if (this.currentStepIndex >= this.sectionMarkers.creation)
        return "Creation";
      return "Welcome";
    },
    getCurrentSectionIcon() {
      if (this.currentStepIndex >= this.sectionMarkers.extras)
        return "mdi-information-outline";
      if (this.currentStepIndex >= this.sectionMarkers.shortcuts)
        return "mdi-keyboard-outline";
      if (this.currentStepIndex >= this.sectionMarkers.presentation)
        return "mdi-presentation";
      if (this.currentStepIndex >= this.sectionMarkers.timeline)
        return "mdi-timeline";
      if (this.currentStepIndex >= this.sectionMarkers.animation)
        return "mdi-animation-outline";
      if (this.currentStepIndex >= this.sectionMarkers.connection)
        return "mdi-connection";
      if (this.currentStepIndex >= this.sectionMarkers.creation)
        return "mdi-shape-outline";
      return "mdi-home-outline";
    },
    ...mapState({
      allSimpleElements: (state) => state.allSimpleElements,
      allSimpleSegments: (state) => state.allSimpleSegments,
      defaultShape: (state) => state.defaultShape,
      diagramMode: (state) => state.diagramMode,
      sequences: (state) => state.sequences,
      tutorial: (state) => state.tutorial,
      currentSequenceId: (state) => state.currentSequenceId,
      continuousMode: (state) => state.rifleMode,
      directorsView: (state) => state.immersiveView,

      currentFrame: (state) =>
        state.sequences.get(state.currentSequenceId)?.currentFrame,
    }),
  },
  watch: {
    // --- Success messages below ---
    allSimpleElements(newVal, oldVal) {
      // Element creation
      if (newVal.size > oldVal.size) {
        if (this.currentStepIndex === 2) {
          this.setSuccessMessageAndBorder(
            "Great job! You've created your first element. Click NEXT to continue.",
          );
        }
        // Check for element creation after shape selection
        else if (this.currentStepIndex === 3 || this.currentStepIndex === 4) {
          this.setSuccessMessageAndBorder(
            `Perfect! You've created an element using the ${this.currentStepIndex === 3 ? "drag-drop" : "double-click"} method. Click NEXT to continue.`,
          );
        }
      }

      // Renaming element
      else if (
        this.currentStepIndex === 6 &&
        newVal.size > 0 &&
        oldVal.size > 0
      ) {
        const renamedElement = Array.from(newVal.values()).find(
          (newElement, index) =>
            newElement.name !== Array.from(oldVal.values())[index]?.name,
        );
        if (renamedElement) {
          this.setSuccessMessageAndBorder(
            "Excellent! You've renamed your element. Click NEXT to continue.",
          );
        }
      }

      // Scaling/resizing element
      else if (
        this.currentStepIndex === 5 &&
        newVal.size > 0 &&
        oldVal.size > 0
      ) {
        const resizedElement = Array.from(newVal.values()).find(
          (newElement, index) =>
            newElement.width !== Array.from(oldVal.values())[index]?.width ||
            newElement.height !== Array.from(oldVal.values())[index]?.height,
        );
        if (resizedElement) {
          this.setSuccessMessageAndBorder(
            "Well done! You've resized your element. Click NEXT to continue.",
          );
        }
      }

      // Color changes
      else if (
        this.currentStepIndex === 7 &&
        newVal.size > 0 &&
        oldVal.size > 0
      ) {
        const colorChanged = Array.from(newVal.values()).some(
          (newElement, index) =>
            newElement.color !== Array.from(oldVal.values())[index]?.color ||
            newElement.textColor !==
              Array.from(oldVal.values())[index]?.textColor,
        );
        if (colorChanged) {
          this.setSuccessMessageAndBorder(
            "Beautiful! You've changed the colors. Click NEXT to continue.",
          );
        }
      }

      // Text size/font changes
      else if (
        this.currentStepIndex === 8 &&
        newVal.size > 0 &&
        oldVal.size > 0
      ) {
        const textPropertiesChanged = Array.from(newVal.values()).some(
          (newElement, index) =>
            newElement.textSize !==
              Array.from(oldVal.values())[index]?.textSize ||
            newElement.textFont !==
              Array.from(oldVal.values())[index]?.textFont ||
            newElement.textWeight !==
              Array.from(oldVal.values())[index]?.textWeight ||
            newElement.name !== Array.from(oldVal.values())[index]?.name,
        );
        if (textPropertiesChanged) {
          this.setSuccessMessageAndBorder(
            "Perfect! You've updated the text properties. Click NEXT to continue.",
          );
        }
      }
    },

    continuousMode(newVal) {
      // Continuous mode enabled
      if (this.currentStepIndex === 31) {
        if (newVal === true) {
          this.setSuccessMessageAndBorder(
            "Continuous Mode is active! Your data flows will play in a continuous loop. Press the button again to disable it. Click NEXT to learn about keyboard shortcuts.",
          );
        } else {
          this.setSuccessMessageAndBorder(
            "Continuous Mode is inactive! Your data flows will play in a normal mode. Press the button again to enable it. Click NEXT to learn about keyboard shortcuts.",
          );
        }
      }
    },

    directorsView(newVal) {
      // Director's View mode enabled
      if (this.currentStepIndex === 30) {
        if (newVal === true) {
          this.setSuccessMessageAndBorder(
            "The Director's View is enabled! The camera will move automatically during playback. Press the button again to disable it. Click NEXT to learn about Continuous Mode.",
          );
        } else {
          this.setSuccessMessageAndBorder(
            "The Director's View is disabled! The camera will not move during playback. Press the button again to enable it. Click NEXT to learn about Continuous Mode.",
          );
        }
      }
    },

    defaultShape() {
      // Store current element count when shape is selected
      if (this.currentStepIndex === 3 || this.currentStepIndex === 4) {
        this.elementsBeforeShapeSelection = this.allSimpleElements.size;
        // Only show intermediate feedback if we're not actually creating elements yet
        if (this.elementsBeforeShapeSelection === this.allSimpleElements.size) {
          this.setSuccessMessageAndBorder(
            "Good shape choice! Now " +
              (this.currentStepIndex === 3
                ? "drag and drop it onto the canvas."
                : "double-click on the canvas to create an element."),
          );
        }
      }
    },
    allSimpleSegments(newVal, oldVal) {
      // Connecting elements
      if (newVal.size > oldVal.size) {
        if (this.currentStepIndex === 10) {
          this.setSuccessMessageAndBorder(
            "Excellent! You've successfully connected two existing elements. Click NEXT to continue.",
          );
        } else if (this.currentStepIndex === 11) {
          this.setSuccessMessageAndBorder(
            "Great job! You've created a new connected element. Click NEXT to continue.",
          );
        }
      }
      // Changing connector style
      else if (
        this.currentStepIndex === 12 &&
        newVal &&
        oldVal &&
        newVal.size === oldVal.size
      ) {
        const newSegments = Array.from(newVal.values());
        const oldSegments = Array.from(oldVal.values());
        // Check if any segment's type changed
        const typeChanged = newSegments.some(
          (newSeg, idx) =>
            newSeg.segmentElementsInfo.mode !==
            oldSegments[idx].segmentElementsInfo.mode,
        );

        if (typeChanged) {
          this.setSuccessMessageAndBorder(
            "Great! You've changed the connector style. Click NEXT to continue.",
          );
        }
      }
    },
    diagramMode(newMode) {
      // Switching to Animation mode
      if (this.currentStepIndex === 14 && newMode === "RECORDING_MODE") {
        this.setSuccessMessageAndBorder(
          "Now you're in Animation mode! Click NEXT to learn about data flow animations.",
        );
      }
      // Switching to Presentation mode
      else if (this.currentStepIndex === 29 && newMode === "PLAYBACK_MODE") {
        this.setSuccessMessageAndBorder(
          "Welcome to Presentation mode! Here you can view and share your completed animations. Click NEXT to continue.",
        );
      }
    },
    sequences(newVal, oldVal) {
      const sequence = Array.from(newVal.values())[0];
      const oldSequence = Array.from(oldVal.values())[0];

      // Creating data flow animation
      if (this.currentStepIndex === 15) {
        if (
          sequence?.frames.some((frame) =>
            frame.some((action) => action.metadata.type === SEND_DATA),
          )
        ) {
          this.setSuccessMessageAndBorder(
            "Great job! You've created a data flow animation. Try creating more or click NEXT to continue.",
          );
        } else if (sequence?.isPlaying) {
          this.setSuccessMessageAndBorder(
            "Excellent! You're watching your animation in action. Click NEXT when you're ready to learn about element movement.",
          );
        }
      }
      // Moving elements animation
      else if (this.currentStepIndex === 16 || this.currentStepIndex === 17) {
        const frame = this.currentStepIndex === 16 ? 3 : 6;

        // First check if we're at the right frame
        if (sequence?.currentFrame === frame) {
          // Then check if there's an element position action at this frame
          const hasNewPositionAction =
            sequence?.frames[frame] &&
            (!oldSequence?.frames[frame] ||
              sequence.frames[frame].some(
                (action) =>
                  action.metadata.type === ELEMENT_POSITION &&
                  !oldSequence.frames[frame].some(
                    (oldAction) =>
                      oldAction.id === action.id &&
                      oldAction.metadata.type === ELEMENT_POSITION,
                  ),
              ));

          if (hasNewPositionAction) {
            this.setSuccessMessageAndBorder(
              `Perfect! You've successfully moved an element at frame ${frame}. ${this.currentStepIndex === 16 ? "Click NEXT to continue element movement." : "Now try playing the animation to see the movement."}`,
            );
          }
        }

        // Also provide feedback when actually playing the animation for step 17
        if (
          this.currentStepIndex === 17 &&
          sequence?.isPlaying &&
          !oldSequence?.isPlaying
        ) {
          this.setSuccessMessageAndBorder(
            "Excellent! You can see how the element moves smoothly between positions. Click NEXT to learn about showing and hiding elements.",
          );
        }
      }

      // Element opacity/visibility changes for step 18
      if (this.currentStepIndex === 18) {
        if (
          sequence.frames.some((frame) =>
            frame.some(
              (action) =>
                action.metadata.type === ELEMENT_OPACTITY &&
                action.data.opacity === 0,
            ),
          )
        ) {
          this.setSuccessMessageAndBorder(
            "Well done! You've learned how to show and hide elements. Click NEXT to learn about camera animations.",
          );
        }
      }

      // Camera animation
      else if (this.currentStepIndex === 19) {
        // Check if there's a new viewbox action that wasn't in the previous state
        const hasNewViewboxAction = sequence?.frames.some((frame) =>
          frame.some((action) => action.metadata.type === VIEWBOX),
        );

        if (hasNewViewboxAction) {
          this.setSuccessMessageAndBorder(
            "Excellent! You've created a camera animation. This adds professional movement to your presentations. Click NEXT to explore the Timeline feature.",
          );
        }
      }
      // Timeline section - Element highlighting
      else if (this.currentStepIndex === 22) {
        // Selection highlighting is hard to detect with watches
        // We'll provide guidance in the UI text
        setTimeout(() => {
          if (this.currentStepIndex === 22) {
            this.setSuccessMessageAndBorder(
              "Try selecting different elements to see how they're highlighted in the timeline. Click NEXT when you're ready to continue.",
            );
          }
        }, 1000);
      }
      // Timeline display toggling - handled in a separate property watch

      // Action deletion
      else if (this.currentStepIndex === 24) {
        const currentActionCount =
          sequence?.frames.reduce(
            (total, frame) =>
              total +
              frame.filter((action) => !action.metadata.interpolated).length,
            0,
          ) || 0;
        const oldActionCount =
          oldSequence?.frames.reduce(
            (total, frame) =>
              total +
              frame.filter((action) => !action.metadata.interpolated).length,
            0,
          ) || 0;
        if (oldActionCount > currentActionCount) {
          this.setSuccessMessageAndBorder(
            "Great job! You've successfully deleted an action from the timeline. Click NEXT to learn about inserting frames.",
          );
        }
      }
      // Frame insertion
      else if (this.currentStepIndex === 25) {
        const frameCount = sequence?.frames.length || 0;
        const oldFrameCount = oldSequence?.frames.length || 0;

        if (frameCount > oldFrameCount) {
          this.setSuccessMessageAndBorder(
            "Perfect! You've inserted a new frame. This creates space for new animation steps. Click NEXT to learn about deleting frames.",
          );
        }
      }
      // Frame deletion
      else if (this.currentStepIndex === 26) {
        const frameCount = sequence?.frames.length || 0;
        const oldFrameCount = oldSequence?.frames.length || 0;

        if (frameCount < oldFrameCount) {
          this.setSuccessMessageAndBorder(
            "Well done! You've deleted a frame from the timeline. Click NEXT to learn about managing sequences.",
          );
        }
      }
      // Managing sequences
      else if (this.currentStepIndex === 27) {
        // We'll check for changes in the manage sequences state
        if (!this.manageSequencesActive && sequence?.manageSequences) {
          this.manageSequencesActive = true;
          this.setSuccessMessageAndBorder(
            "Great! You've enabled sequence management. Try creating, renaming, or deleting sequences.",
          );
        }

        // Check for sequence operations
        const sequenceCountChanged = newVal.size !== oldVal.size;
        const sequenceNameChanged = sequence?.name !== oldSequence?.name;

        if (sequenceCountChanged || sequenceNameChanged) {
          this.setSuccessMessageAndBorder(
            `Well done! You've ${sequenceCountChanged ? (newVal.size > oldVal.size ? "created a new" : "deleted a") : "renamed your"} sequence. Click NEXT to continue to Presentation mode.`,
          );
        }
      }

      // Playback in presentation mode
      else if (
        this.currentStepIndex === 29 &&
        sequence?.isPlaying &&
        !oldSequence?.isPlaying
      ) {
        this.setSuccessMessageAndBorder(
          "Great! You're now playing your animation. Try using the timeline to jump to specific frames. Click NEXT to learn about Director's View.",
        );
      }
    },
    currentFrame(newVal, oldVal) {
      // Navigating to frame 3
      if (this.currentStepIndex === 16 && oldVal < newVal && newVal === 3) {
        this.setSuccessMessageAndBorder(
          "Good! You've reached frame 3. Now try moving an element to a new position.",
        );
      }
      // Navigating to frame 6
      else if (
        this.currentStepIndex === 17 &&
        oldVal < newVal &&
        newVal === 6
      ) {
        this.setSuccessMessageAndBorder(
          "Perfect! You've reached frame 6. Now move the same element again to create a smooth animation path.",
        );
      }
      // Manual timeline navigation in presentation mode
      else if (this.currentStepIndex === 29 && oldVal !== newVal) {
        this.setSuccessMessageAndBorder(
          "Excellent job navigating the timeline! You can scrub through your animation by dragging the marker. Click NEXT to learn about Director's View.",
        );
      }
    },
    async isVisible(visible) {
      if (!this.tutorial.started && visible) {
        this.$emit("updateTourGuide", false);
        this.workspaceBefore = holdWorkspace();
        await resetAll();
        await fetchWorkspace(this.tutorialWorkspace);
        await this.updateTutorialAction({
          started: true,
          currentStep: this.currentStepIndex,
        });
      }
    },
  },
  methods: {
    ...mapActions(["updateTutorialAction"]),
    gotoFrame(index) {
      this.resetButtons();
      this.currentStepIndex = index;

      // Reset state tracking variables when changing steps
      if (index === 23) {
        this.displayTimelineState = null;
      } else if (index === 24) {
        this.previousActionCount = null;
      } else if (index === 25 || index === 26) {
        this.previousFrameCount = null;
      } else if (index === 27) {
        this.manageSequencesActive = false;
      }

      const el = document.getElementById(this.currentStep.element);
      if (el) {
        el.style.border = "3px solid yellow";
      }
      if (this.currentStep.position) {
        this.cardStyle = {
          textAlign: "center",
          width: "400px",
          position: "absolute",
          top: `${this.currentStep.position.top}%`,
          left: `${this.currentStep.position.left}%`,
          transform: "translate(-50%, -50%)",
          zIndex: "2000",
          color: "#b1dcdd",
          backgroundColor: "#302f2f",
        };
      }
      this.overlayStyle = {
        position: "fixed",
        top: "0",
        left: "0",
        width: "100%",
        height: "100%",
        background: this.currentStep.overlay
          ? "rgba(0, 0, 0, 0.7)"
          : "rgba(0, 0, 0, 0)",
        zIndex: this.currentStep.overlay ? "1500" : "0",
        pointerEvents: this.currentStep.overlay ? "auto" : "none",
      };
    },
    restart() {
      this.gotoFrame(0);
      this.resetButtons();
      this.resetSuccessMessages();
      resetAll();
    },
    previousFrame() {
      if (this.currentStepIndex > 0) {
        this.gotoFrame(this.currentStepIndex - 1);
      }
    },
    nextFrame() {
      if (this.currentStepIndex < this.steps.length - 1) {
        this.gotoFrame(this.currentStepIndex + 1);

        // Set success messages for extras section
        if (this.currentStepIndex === 36 || this.currentStepIndex === 37) {
          this.setExtrasSuccessMessage(this.currentStepIndex);
        }
      } else {
        // End of tutorial
        this.$emit("close");
      }
    },
    close() {
      this.tutorialWorkspace = holdWorkspace();
      fetchWorkspace(this.workspaceBefore);
      this.updateTutorialAction({
        started: false,
        currentStep: this.currentStepIndex,
      });
      this.$emit("updateTutorialDialog", false);
      this.resetButtons();
    },
    resetButtons() {
      const el = document.getElementById("tutorialNextStepButton");
      if (el) {
        el.style.border = "none";
      }
      this.steps.forEach((frame) => {
        if (frame.element) {
          const el = document.getElementById(frame.element);
          if (el) el.style.border = "none";
        }
      });
    },
    resetSuccessMessages() {
      this.steps.forEach((frame) => {
        frame.successMessage = null;
      });
    },
    setSuccessMessageAndBorder(message) {
      const el = document.getElementById("tutorialNextStepButton");
      if (el) {
        el.style.border = "6px solid #8eff7d";
      }
      this.currentStep.successMessage = message;
    },
    // Method to load workspace from URL for current step
    async loadWorkspaceForStep() {
      if (!this.currentStep.workspaceUrl) return;

      this.isLoading = true;
      this.workspaceError = null;
      try {
        // Fetch example workspace from URL
        const response = await fetch(this.currentStep.workspaceUrl);
        if (!response.ok) {
          throw new Error(`Failed to load workspace: ${response.statusText}`);
        }

        const workspaceData = await response.json();

        // Load the fetched workspace
        await loadWorkspaceFromDeserialisedContent(
          JSON.stringify(workspaceData),
          "Error loading tutorial workspace",
        );

        // Reset tutorial state to true as it goes back to false after loading the workspace
        this.updateTutorialAction({
          started: true,
          currentStep: this.currentStepIndex,
        });

        // Success message
        this.setSuccessMessageAndBorder(
          "Example workspace loaded! Continue with the tutorial.",
        );
      } catch (error) {
        console.error("Error loading workspace:", error);
        this.workspaceError = `Error loading workspace: ${error.message}`;
        this.currentStep.successMessage = null;
      } finally {
        this.isLoading = false;
      }
    },
    // Method to show keyboard shortcut info
    handleShortcutDemo(e) {
      if (this.currentStepIndex === 33) {
        const shortcutUsed =
          e.key === "Delete" ||
          (e.ctrlKey && e.key === "z") ||
          (e.ctrlKey && e.shiftKey && e.key === "Z") ||
          e.key === " " ||
          (e.ctrlKey && e.key === "s") ||
          e.key === "Escape" ||
          e.key.startsWith("Arrow");

        if (shortcutUsed) {
          this.setSuccessMessageAndBorder(
            `Great! You've used the ${e.key === " " ? "SPACE" : e.key} ${e.ctrlKey ? "with CTRL" : ""} ${e.shiftKey ? "with SHIFT" : ""} shortcut. Try others or click NEXT to continue.`,
          );
        }
      }
    },
    // Add success messages for the extras section
    setExtrasSuccessMessage(stepIndex) {
      if (stepIndex === 35) {
        this.setSuccessMessageAndBorder(
          "Now you know how to share your diagrams using URL links! This makes it easy to include your animations in websites or documentation. Click NEXT to continue.",
        );
      }
    },
    skipToSection(section) {
      if (section in this.sectionMarkers) {
        this.currentStepIndex = this.sectionMarkers[section];
      }
    },
    nextStep() {
      this.nextFrame();
    },
  },
  mounted() {
    // Add keyboard event listener for shortcut steps
    window.addEventListener("keydown", this.handleShortcutDemo);

    // Watch for timeline display toggle - we need to intercept the checkbox event
    if (typeof document !== "undefined") {
      const observer = new MutationObserver(() => {
        if (this.currentStepIndex === 23) {
          const displayTimelineCheckbox = document.querySelector(
            'input[type="checkbox"][aria-label="Display Timeline"]',
          );
          if (
            displayTimelineCheckbox &&
            this.displayTimelineState !== displayTimelineCheckbox.checked
          ) {
            this.displayTimelineState = displayTimelineCheckbox.checked;
            this.setSuccessMessageAndBorder(
              `Great! You've ${this.displayTimelineState ? "displayed" : "hidden"} the timeline. Click NEXT to learn about deleting sequence actions.`,
            );
          }
        }

        if (this.currentStepIndex === 27) {
          const manageSequencesCheckbox = document.querySelector(
            'input[type="checkbox"][aria-label="Manage Sequences"]',
          );
          if (
            manageSequencesCheckbox &&
            !this.manageSequencesActive &&
            manageSequencesCheckbox.checked
          ) {
            this.manageSequencesActive = true;
            this.setSuccessMessageAndBorder(
              "Great! You've enabled sequence management. Try creating, renaming, or deleting sequences.",
            );
          }
        }
      });

      observer.observe(document.body, {
        subtree: true,
        childList: true,
        attributes: true,
      });

      this._observer = observer;
    }
  },
  beforeUnmount() {
    // Remove keyboard event listener when component is destroyed
    window.removeEventListener("keydown", this.handleShortcutDemo);

    // Clean up observer
    if (this._observer) {
      this._observer.disconnect();
    }
  },
};
</script>

<style scoped>
.tutorial-sections {
  margin-bottom: 15px;
  padding: 0 15px;
}

.tutorial-sections p {
  margin-bottom: 5px;
  font-weight: bold;
}

.section-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.workspace-loader {
  margin-top: 15px;
  padding: 0 15px;
  margin-bottom: 15px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 15px;
}

.workspace-loader p {
  margin-bottom: 10px;
  font-weight: bold;
  color: #d9eaf9;
}

.tutorial-title {
  font-weight: bold !important;
  font-size: xx-large !important;
  color: #4fc3f7 !important;
  background: linear-gradient(45deg, #4fc3f7, #81c784);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 5px;
}

.current-section-header {
  margin-bottom: 15px;
  text-align: center;
}

.navigation-card {
  margin: 10px;
  flex-basis: 280px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
}

.navigation-title {
  font-size: medium !important;
  padding: 10px 16px !important;
  background-color: #f5f5f5;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.navigation-actions {
  flex-wrap: wrap;
  justify-content: center;
  padding: 8px;
}

.section-btn {
  margin: 4px;
}
</style>
