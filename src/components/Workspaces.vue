<template>
  <div class="workspaces">
    <div class="workspaces-header">
      <h3>Workspaces</h3>
    </div>

    <div class="workspaces-list">
      <div
        v-for="workspace in workspaces"
        :key="workspace.id"
        class="workspace-item"
        :class="{ active: workspace.id === currentWorkspaceId }"
        @click="enterWorkspace(workspace.id)"
      >
        <div class="workspace-info">
          <span class="workspace-name">{{ workspace.name }}</span>
          <span class="workspace-parent">
            (Parent: {{ workspace.parentElementName }})
          </span>
        </div>
        <div class="workspace-buttons">
          <button
            v-if="
              workspace.id !== rootWorkspaceId &&
              workspace.id !== currentWorkspaceId
            "
            class="action-button add-button"
            @click.stop="addToCurrentWorkspace(workspace.id)"
            title="Add to current workspace"
          >
            <v-icon>mdi-plus</v-icon>
          </button>
          <button
            v-if="workspace.id !== rootWorkspaceId"
            class="action-button delete-button"
            @click.stop="confirmDelete(workspace.id)"
            title="Delete workspace"
          >
            <v-icon>mdi-delete</v-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import {
  addWorkspaceToCurrentWorkspace,
  deleteWorkspace,
  enterWorkspace,
} from "@/core";

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Workspaces",
  computed: {
    ...mapState({
      currentWorkspaceId: (state) => state.currentWorkspaceId,
      rootWorkspaceId: (state) => state.rootWorkspaceId,
      allSimpleElements: (state) => state.allSimpleElements,
    }),
    ...mapGetters(["workspaces"]),
  },
  methods: {
    async enterWorkspace(workspaceId) {
      await enterWorkspace(workspaceId, true);
    },

    async confirmDelete(workspaceId) {
      await deleteWorkspace(workspaceId);
    },

    async addToCurrentWorkspace(workspaceId) {
      await addWorkspaceToCurrentWorkspace(workspaceId);
    },
  },
};
</script>

<style scoped>
.workspaces {
  padding: 16px;
}

.workspaces-header {
  margin-bottom: 16px;
}

.workspaces-header h3 {
  margin: 0;
  font-size: 1.2em;
}

.workspaces-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.workspace-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.workspace-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.workspace-item.active {
  background-color: rgba(0, 0, 0, 0.1);
}

.workspace-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.workspace-name {
  font-weight: 500;
}

.workspace-parent {
  font-size: 0.9em;
  color: #666;
}

.workspace-buttons {
  display: flex;
  gap: 4px;
}

.action-button {
  padding: 4px;
  border: none;
  background: none;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.delete-button:hover {
  color: #ff4444;
}

.add-button:hover {
  color: #44ff44;
}
</style>
