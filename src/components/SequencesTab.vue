<template>
  <v-card>
    <v-tabs v-model="sequencesSubHeaderTab" bg-color="#2a2d32ff" fixed-tabs>
      <v-tab value="list" style="color: #eddb99">List</v-tab>
      <v-tab value="frame" style="color: #eddb99">Frame</v-tab>
    </v-tabs>
    <v-window
      v-model="sequencesSubHeaderTab"
      style="background-color: #2a2d32ff"
    >
      <v-window-item value="list">
        <v-card color="#2a2d32ff">
          <v-card-title style="color: white">
            List of all Sequences
          </v-card-title>
          <v-card-subtitle>
            <v-btn color="#2a2d32ff" @click="sequenceCreation">
              <v-icon style="color: white"> mdi-plus </v-icon>
            </v-btn>
          </v-card-subtitle>
          <v-card
            v-for="(sequence, index) in getSequences"
            v-bind:key="index"
            color="#2a2d32ff"
          >
            <v-btn @click="sequenceSelect(sequence.id)" color="#2a2d32ff">
              <v-chip style="color: white">
                {{ sequence.name }}
              </v-chip>
            </v-btn>
            <v-btn color="#2a2d32ff" @click="sequenceDelete(sequence.id)">
              <v-icon style="color: white"> mdi-delete </v-icon>
            </v-btn>
          </v-card>
        </v-card>
      </v-window-item>
      <v-window-item value="frame">
        <!-- Outer card as usual -->
        <v-card color="#2a2d32ff" class="pa-2">
          <v-card-title style="color: white; padding: 0; font-size: 16px">
            Frame {{ Math.floor(currentSequence.currentFrame) }}
          </v-card-title>
          <!-- ================== FRAME DURATION EDIT (improved layout) ================== -->
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model.number="frameDurationComputed"
                label="Frame Duration"
                type="number"
                style="color: white"
                step="0.1"
                min="0.1"
                max="10"
              />
            </v-col>
          </v-row>
          <!-- ================== END FRAME DURATION ================== -->

          <!-- ================== ACTIONS FOR THIS FRAME ================== -->
          <v-row
            v-if="
              currentSequence &&
              (currentSequence.frames[
                Math.floor(currentSequence.currentFrame)
              ] ||
                currentSequence.frames[
                  Math.floor(currentSequence.currentFrame) + 1
                ])
            "
            class="mt-2"
          >
            <v-col cols="12">
              <span style="color: white; font-weight: 800">
                Actions in frame interval
                {{ Math.floor(currentSequence.currentFrame) }} -
                {{ Math.floor(currentSequence.currentFrame) + 1 }}
              </span>
            </v-col>
            <!-- Loop over actions. For each action type, display a consistent layout -->
            <v-col
              v-for="(action, actionIndex) in currentSequence.frames[
                Math.floor(currentSequence.currentFrame) + 1
              ]?.filter(
                (filteredAction) => filteredAction.metadata.type === 'sendData',
              )"
              :key="actionIndex"
              cols="12"
            >
              <v-card color="#2a2d32ff" class="my-2 pa-2">
                <!-- ========== TYPE: sendData ========== -->
                <template v-if="action.metadata.type === 'sendData'">
                  <v-card-title
                    style="color: white; padding: 0; font-size: 16px"
                  >
                    Data Flow
                  </v-card-title>
                  <!-- Exactly like BUILD: show source & destination with chips horizontally -->
                  <v-chip
                    class="ma-1"
                    :style="getElementStyle(action.data.elementSource)"
                  >
                    {{
                      allSimpleElements.get(action.data.elementSource).name
                        .length > 8
                        ? allSimpleElements
                            .get(action.data.elementSource)
                            .name.slice(0, 8) + "..."
                        : allSimpleElements.get(action.data.elementSource).name
                    }}
                  </v-chip>
                  <v-icon class="ma-1" :style="{ color: action.data.color }"
                    >mdi-play</v-icon
                  >
                  <v-chip
                    class="ma-1"
                    :style="getElementStyle(action.data.elementDestination)"
                  >
                    {{
                      allSimpleElements.get(action.data.elementDestination).name
                        .length > 8
                        ? allSimpleElements
                            .get(action.data.elementDestination)
                            .name.slice(0, 8) + "..."
                        : allSimpleElements.get(action.data.elementDestination)
                            .name
                    }}
                  </v-chip>
                  <!-- Then show a row for “duration” and a row for “color,” each on its own line -->
                  <v-row class="mt-2">
                    <v-col cols="12">
                      <v-text-field
                        v-model.number="action.metadata.duration"
                        label="Action Duration"
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="10"
                        style="color: white"
                        @change="updateSequence()"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12">
                      Data Flow Color:
                      <v-color-picker
                        v-model="action.data.color"
                        mode="rgb"
                        hide-inputs
                        canvas-height="100"
                        color="#2a2d32ff"
                        @change="updateSequence()"
                      ></v-color-picker>
                    </v-col>
                  </v-row>
                </template>
              </v-card>
            </v-col>
            <v-col
              v-for="(action, actionIndex) in currentSequence.frames[
                Math.floor(currentSequence.currentFrame) + 1
              ]?.filter(
                (filteredAction) =>
                  filteredAction.metadata.type === 'elementPosition' ||
                  filteredAction.metadata.type === 'viewboxPosition',
              )"
              :key="actionIndex"
              cols="12"
            >
              <v-card color="#2a2d32ff" class="my-2 pa-2">
                <!-- ========== TYPE: ELEMENT_POSITION, not interpolated ========== -->
                <template v-if="action.metadata.type === ELEMENT_POSITION()">
                  <v-card-title
                    style="color: white; padding: 0; font-size: 16px"
                  >
                    Position
                  </v-card-title>
                  <v-chip
                    class="ma-1"
                    :style="getElementStyle(action.data.elementId)"
                  >
                    {{
                      allSimpleElements.get(action.data.elementId).name.length >
                      30
                        ? allSimpleElements
                            .get(action.data.elementId)
                            .name.slice(0, 30) + "..."
                        : allSimpleElements.get(action.data.elementId).name
                    }}
                  </v-chip>

                  <!-- Then each parameter on its own row -->
                  <v-row class="mt-2">
                    <v-col cols="12">
                      <v-text-field
                        v-model.number="action.metadata.duration"
                        label="Duration"
                        style="color: white"
                        @change="updateSequence()"
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="10"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </template>

                <!-- ========== TYPE: viewboxPosition ========== -->
                <template
                  v-else-if="action.metadata.type === 'viewboxPosition'"
                >
                  <v-card-title
                    style="color: white; padding: 0; font-size: 16px"
                  >
                    View Position
                  </v-card-title>
                  <v-icon icon="mdi-camera" color="#d3fdbd"></v-icon>
                  <v-row class="mt-2">
                    <v-col cols="12">
                      <v-text-field
                        v-model.number="action.metadata.duration"
                        label="Duration"
                        style="color: white"
                        @change="updateSequence()"
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="10"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </template>
              </v-card>
            </v-col>
          </v-row>
          <!-- ================== END ACTIONS ================== -->
        </v-card>
      </v-window-item>
    </v-window>
  </v-card>
</template>

<script>
import { mapActions, mapState } from "vuex";
import {
  createSequence,
  deleteSequence,
  ELEMENT_POSITION,
  selectSequence,
  VIEWBOX,
} from "@/sequence";
import { setupObjectsAtFrame } from "@/animation";

export default {
  name: "SequencesTab",
  methods: {
    ELEMENT_POSITION() {
      return ELEMENT_POSITION;
    },
    VIEWBOX() {
      return VIEWBOX;
    },
    ...mapActions([
      "updateSequenceAction",
      "updateRightDrawerSequencesSubTabAction",
    ]),
    getElementStyle(elementId) {
      return {
        color: this.allSimpleElements.get(elementId)?.textColor || "grey",
        background: this.allSimpleElements.get(elementId)?.color,
      };
    },
    async sequenceCreation() {
      await createSequence({ undoable: false });
    },
    sequenceDelete(sequenceId) {
      deleteSequence({ sequenceId, undoable: false, recreateDefault: true });
    },
    sequenceSelect(sequenceId) {
      selectSequence(
        sequenceId ||
          Array.from(this.sequences.values()).find(
            (sequence) => sequence.name === this.textSequenceCurrentSelection,
          ).id,
      );
      setupObjectsAtFrame(
        sequenceId,
        this.sequences.get(this.currentSequenceId).currentFrame,
      );
    },
    updateSequence() {
      const sequence = this.currentSequence;
      this.updateSequenceAction({
        id: sequence.id,
        sequence: sequence,
        undoable: true,
      });
    },
    updateFrameDuration() {
      this.currentSequence.frames[
        Math.floor(this.currentSequence.currentFrame) + 1
      ].forEach((action) => {
        action.metadata.duration = this.frameDuration;
      });
      this.updateSequence();
    },
    adjustForScreenSize() {
      this.isSmallScreen = window.innerWidth < 768;
    },
  },
  mounted() {
    this.adjustForScreenSize();
    window.addEventListener("resize", this.adjustForScreenSize);
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.adjustForScreenSize);
  },
  components: {},
  computed: {
    ...mapState({
      allSimpleElements: (state) => state.allSimpleElements,
      sequences: (state) => state.sequences,
      currentSequenceId: (state) => state.currentSequenceId,
      selectedElementsIds: (state) => state.selectedElementsIds,
      selectedTextsIds: (state) => state.selectedTextsIds,
      selectedSegmentsIds: (state) => state.selectedSegmentsIds,
      millisecondsPerProcessingUnit: (state) =>
        state.millisecondsPerProcessingUnit,
      diagramMode: (state) => state.diagramMode,
      snapshots: (state) => state.snapshots,
      recordingVideo: (state) => state.recordingVideo,
      selectedElements: (state) =>
        Array.from(state.allSimpleElements.values()).filter((element) =>
          state.selectedElementsIds.includes(element.id),
        ),
      sequencesSubHeaderTabStore: (state) => state.rightDrawerSequencesSubTab,
    }),
    currentSequence() {
      return this.sequences.get(this.currentSequenceId);
    },
    getSequences() {
      return Array.from(this.sequences.values());
    },
    textSequenceCurrentSelection: {
      get: function () {
        return this.currentSequence ? this.currentSequence.name : null;
      },
      set: function (value) {
        this.sequenceSelect(
          Array.from(this.sequences.values()).find(
            (sequence) => sequence.name === value,
          ).id,
        );
      },
    },
    sequencesSubHeaderTab: {
      get: function () {
        return this.sequencesSubHeaderTabStore;
      },
      set: function (value) {
        this.updateRightDrawerSequencesSubTabAction(value);
      },
    },
    frameDurationComputed: {
      get() {
        // Assuming your frame index is calculated like this:
        const frameIndex = Math.floor(this.currentSequence.currentFrame) + 1;
        const frameActions = this.currentSequence.frames[frameIndex];
        if (frameActions && frameActions.length) {
          // Compute the maximum duration among all actions in the frame.
          return Math.max(
            ...frameActions.map((action) => action.metadata.duration),
          );
        }
        // Fallback default if no actions exist.
        return 1;
      },
      set(newDuration) {
        const frameIndex = Math.floor(this.currentSequence.currentFrame) + 1;
        const frameActions = this.currentSequence.frames[frameIndex];
        if (frameActions) {
          frameActions.forEach((action) => {
            action.metadata.duration = newDuration;
          });
          this.updateSequence();
        }
      },
    },
  },
  data: function () {
    return {
      isSmallScreen: false,
    };
  },
};
</script>

<style scoped>
.v-color-picker-edit__input input {
  background: yellow;
}
</style>
