const drawAdapter = {
  createElement: jest.fn(),
  highlightElement: jest.fn(),
  unhighlightElement: jest.fn(),
  deleteElement: jest.fn(),
  updateGraphicElementFromState: jest.fn(),
  showItemPositions: jest.fn(),
  hideItemPositions: jest.fn(),
  traceSegment: jest.fn(),
  deleteSegment: jest.fn(),
  createConnectionBetweenElements: jest.fn(),
  resetEntities: jest.fn(),
  // Add any other necessary methods here
};

export default drawAdapter;
