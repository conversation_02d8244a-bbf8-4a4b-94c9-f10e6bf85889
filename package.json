{"name": "simulaction", "version": "0.69.0", "private": true, "scripts": {"test:unit": "jest", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "invalidate": "aws --profile simulaction-deployer --region ap-southeast-2 cloudfront create-invalidation --distribution-id EU280HAXOV99K --paths '/*'", "deploy": "vue-cli-service build && aws --profile simulaction-deployer --region ap-southeast-2 s3 sync ./dist s3://www.simulaction.io --delete"}, "dependencies": {"@mdi/font": "^7.2.96", "@svgdotjs/svg.draggable.js": "^3.0.2", "@svgdotjs/svg.js": "^3.2.0", "@svgdotjs/svg.panzoom.js": "^2.1.2", "core-js": "^3.8.3", "jsdom": "^24.1.0", "util": "^0.12.5", "uuid": "^8.3.2", "vue": "^3.2.13", "vue-router": "4", "vuetify": "^3.3.5", "vuex": "^4.0.2", "webfontloader": "^1.0.0", "yarn": "^1.22.19"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/preset-env": "^7.24.7", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.0", "@vue/compiler-sfc": "^3.4.27", "@vue/test-utils": "^2.4.0-alpha.2", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "3.3.1", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-jest": "^5.0.0-alpha.10", "webpack-plugin-vuetify": "^2.0.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}